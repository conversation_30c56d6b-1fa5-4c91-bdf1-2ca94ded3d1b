<{if $dataList}>
<style>
	.text_11{
		width: 5.3125rem;
	}
</style>
    <{foreach from=$dataList item=item}>
	<div class="list-item">
		<div class="text-wrapper_5 flex-row justify-between">
			<{if $item.order_create_date}>
			<span class="text_11">下单时间：</span>
			<span class="text_12" style="color: #999999;"><{$item.order_create_date}></span>
			<{/if}>
			<span class="text_13"><{$item.status_name}></span>
		</div>
		<div class="text-wrapper_5 flex-row justify-between">
			<span class="text_11">渠道：</span>
			<span class="text_12"><{$item.shop_type_name}></span>
			<{if $item.deadline}>
			<span class="text_13 countdown" data-deadline="<{$item.deadline}>"></span>
			<{/if}>
		</div>
		<div class="text-wrapper_5 flex-row justify-between">
			<span class="text_11">门店：</span>
			<span class="text_12"><{$item.store_name}></span>
		</div>
		<div class="text-wrapper_5 flex-row justify-between">
			<span class="text_11">订单号：</span>
			<span class="text_12"><{$item.order_bn}>
				<{if $item.is_change_order}>
					<span style="color: #FF0000">换货</span>
				<{/if}>
			</span>
		</div>
		<div class="text-wrapper_5 flex-row justify-between">
			<span class="text_11">售后申请单号：</span>
			<span class="text_12"><{$item.return_bn}></span>
		</div>
		<div class="text-wrapper_5 flex-row justify-between">
			<span class="text_11">售后申请时间：</span>
			<span class="text_12"><{$item.add_time_format}></span>
			<{if $item.return_logi_status_name}>
			<span class="status-order"><{$item.return_logi_status_name}></span>
			<{/if}>
			<{if $item.is_history == 'true'}>
			<span class="status-order">历史</span>
         	<{ /if}>
		</div>
		<{if $item.return_logi_no}>
		<div class="text-wrapper_5 flex-row justify-between">
			<span class="text_11">退货运单号：</span>
			<span class="text_12"><{$item.return_logi_no}></span>
		</div>
		<{/if}>
		<div class="text-wrapper_5 flex-row pr">
			<span class="text_11">收件人：</span>
			<span class="text_26 sensitive-text" data-decryptAddress="false" data-orderid="<{$item.order_id}>" data-raw="<{$item.ship_name}>（<{$item.ship_mobile}>）"
				  data-masked="<{$item.ship_name}>（<{$item.ship_mobile}>）"><{$item.ship_name}>（<{$item.ship_mobile}>）</span>
			<span class="text_27 copy-btn" data-copy="<{$item.decrypt_ship_mobile}>">复制</span>
			<div class="toggle-visibility label_1">
				<i class="eye-icon hidden"></i>
			</div>
			<!-- <img class="label_1 toggle-visibility" referrerpolicy="no-referrer" src="./icon-eye.png" /> -->
		</div>
		<div class="text-wrapper_5 flex-row">
			<span class="text_11">收货地址：</span>
			<span class="text_26 sensitive-text flex-shrink-1" data-raw="<{$item.ship_addr}>" data-masked="<{$item.ship_addr}>"><{$item.ship_addr}></span> <span
				class="text_27 copy-btn" data-copy="<{$item.ship_addr}>">复制</span>
		</div>

		<{if $item.custom_remark_text}>
		<div class="text-wrapper_5 flex-row">
			<span class="text_11">买家备注：</span>
			<div class="flex-row align-center justify-between" style="flex: 1;">
				<span class="remark-content"><{$item.custom_remark_text}></span>
				<span class="arrow-icon"></span>
			</div>
		</div>
		<{/if}>

		<{if $item.order_remark_text}>
		<div class="text-wrapper_5 flex-row">
			<span class="text_11">客服备注：</span>
			<div class="flex-row align-center justify-between" style="flex: 1;">
				<span class="remark-content"><{$item.order_remark_text}></span>
				<span class="arrow-icon"></span>
			</div>
		</div>
		<{/if}>

		<div class="hr"></div>
		<!-- goodsInfo -->
		<{foreach from=$item.return_product_items item=data_item}>
			<div class="section_6 flex-row">
				<img class="image_2" referrerpolicy="no-referrer"
					 src="<{$data_item.goods_img_url|default:'/app/wap/statics/img/default_goods_img.png'}>" />
				<div class="text-group_1 flex-col">
					<span class="text_28"><{$data_item.name}></span>
					<span class="text_29">商户货号：<{$data_item.busness_material_bn}></span>
					<span class="text_30"><{$data_item.num}>件；<{$data_item.specifications}></span>
					<span class="text_31">￥<{$data_item.amount}></span>
				</div>
			</div>
		<{/foreach}>
		<{if $item.change_items}>
		<div class="return_goods">
			<{if $item.reship_bn}>
			<div class="text-wrapper_5 flex-row justify-between align-center">
				<span class="text_32">换货单号：</span>
				<span class="text_33"><{$item.reship_bn}></span>
			</div>
			<{/if}>
			<div class="text-wrapper_5 flex-row justify-between align-center">
				<span class="text_32">申请理由：</span>
				<span class="text_33"><{$item.content}></span>
			</div>
			<{foreach from=$item.change_items item=citem}>
				<div class="section_6 flex-row">
					<img class="image_2" referrerpolicy="no-referrer"
						 src="<{$citem.goods_img_url|default:'/app/wap/statics/img/default_goods_img.png'}>" />
					<div class="text-group_1 flex-col">
						<div class="status-info"> <span class="text_28 flex-shrink-1"><{$citem.name}></span>
						</div>
						<span class="text_29">商品货号：<{$citem.bn}></span>
						<span class="text_30"><{$citem.num}>件；<{$citem.specifications}></span>
						<!-- <span class="text_31">￥1800.00</span> -->
					</div>
				</div>
			<{/foreach}>
		</div>
		<{/if}>

		<!-- total -->
		<{if $item.status_name == '已退款'}>
			<div class="section_6 justify-between flex-wrap">
			  <div class="mrg-side-4">
				<span class="text_36">买家</span><span class="text_37">￥<{$item.total_amount}></span>
			  </div>
			  <div class="mrg-side-4">
				<span class="text_36">优惠</span><span class="text_37">￥<{$item.esb_pmt_amount}></span>
			  </div>
			  <div class="mrg-side-4">
				<span class="text_36">商家</span><span class="text_37">￥<{$item.esb_amount}></span>
			  </div>
			</div>
        <{else}>
			<div class="text-wrapper_11 flex-row">
				<span class="text_36">总价：</span>
				<span class="text_37">￥<{$item.total_amount}></span>
			</div>
        <{/if}>
		<div class="section_7 flex-row justify-end">
			<{if $item.show_logistics_button}>
				<div class="text-wrapper_12 flex-col">
					<span class="text_38" onclick="look_modal_logistics('<{$item.return_id}>')">查看物流</span>
				</div>
			<{/if}>
			<{if $item.show_pending_button}>
				<div class="text-wrapper_12 flex-col">
					<span class="text_38" onclick="showAfterSaleModal('<{$item.return_id}>','<{$item.content}>')">售后申请审核</span>
				</div>
			<{/if}>
			<{if $item.show_intercept_button}>
				<div class="text-wrapper_12 flex-col">
					<a class="text_38" style="text-decoration:none;" href="<{$item.intercept_href}>">拦截登记</a>
				</div>
			<{/if}>
			<{if $item.show_reship_refund_button}>
				<div class="text-wrapper_12 flex-col">
					<span class="text_38" onclick="doRefundReship('<{$item.reship_id}>')">确认退货退款</span>
				</div>
			<{/if}>

			<{if $item.show_reship_button}>
				<div class="text-wrapper_12 flex-col">
					<a class="text_38" style="text-decoration:none;" href="<{$item.reship_href}>">退货商品审核</a>
				</div>
			<{/if}>
		</div>
	</div>
        
    <{/foreach}>
<{/if}>
