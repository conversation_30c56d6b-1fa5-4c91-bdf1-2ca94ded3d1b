<?php

/**
 * 失败预警
 * Class invoice_autotask_timer_failwarning
 */
class invoice_autotask_timer_failwarning
{
    public function process($params, &$error_msg = '')
    {
        $db = kernel::database();
        $shop_type = XCXSHOPTYPE;
        $last_modify = time()-7200;
        $three_days_ago = strtotime('-3 days');

        $sql = "select order_bn,sync_msg,sync,shop_sync_status,invoice_apply_bn from sdb_invoice_order where sync in ('2','5') or shop_sync_status in ('3') and shop_type = '$shop_type' and last_modify > $three_days_ago and last_modify< $last_modify";
        $res = $db->select($sql);

        $order_dbschema = app::get('invoice')->model('order')->get_schema();

        $error_msg_arr = [];
        foreach ($res as $res_info) {
            $order_bn = $res_info['order_bn'];
            $invoice_apply_bn = $res_info['invoice_apply_bn'];
            if (in_array($res_info['sync'], ['2', '5'])) {
                $sync_name = $order_dbschema['columns']['sync']['type'][$res_info['sync']];
                $error_msg = "开票申请单号：$invoice_apply_bn, 订单号:".$order_bn . $sync_name . " 失败原因:" . $res_info['sync_msg'];
            } else if (in_array($res_info['shop_sync_status'], ['3'])) {
                $error_msg = "开票申请单号：$invoice_apply_bn, 订单号:".$order_bn . " 同步平台失败，失败原因:" . $res_info['shop_sync_msg'];
            }
            $error_msg_arr[] = $error_msg;
        }

        $all_error_msg = implode("<br>",$error_msg_arr);

        //发送邮件
        kernel::single('monitor_event_notify')->addNotify('invoice_fail_warning', [
            'sync_msg' => $all_error_msg,
        ], true);

    }
}