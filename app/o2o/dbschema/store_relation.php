<?php
/**
 * ShopEx licence
 *
 * @copyright  Copyright (c) 2005-2010 ShopEx Technologies Inc. (http://www.shopex.cn)
 * @license  http://ecos.shopex.cn/ ShopEx License
 */

$db['store_relation']=array (
    'columns' =>
    array (
        'relation_id' =>
            array (
                'type' => 'int unsigned',
                'required' => true,
                'pkey' => true,
                'extra' => 'auto_increment',
                'editable' => false,
            ),
        'store_id' =>
        array (
            'type'     => 'table:store@o2o',
            'label'    => '门店id',
            'width'    => 150,
            'editable' => false,
            'in_list'  => true,
            'comment'  => '门店id'
        ),
        'store_bn' =>
        array (
            'type' => 'varchar(50)',
            'required' => true,
            'default' => '',
            'label'=> '门店编码',
            'width'=>100,
            'default_in_list'=>true,
            'in_list'=>true,
            'editable' => false,
        ),
        'p_relation_id' =>
        array (
            'type' => 'int unsigned',
            'editable' => false,
            'comment' => '父ID',
            'label'   => '父节点',
            'default' => 0,
            'default_in_list'=>true,
            'in_list'=>true,
        ),
        'top_relation_id' =>
            array (
                'type' => 'int unsigned',
                'editable' => false,
                'comment' => '最顶级父关系',
                'default' => 0,
                'default_in_list'=>true,
                'in_list'=>true,
                'label'   => '最顶级父关系',
            ),
        'pos' =>
            array (
                'type' => 'int(6)',
                'editable' => false,
                'comment' => '排序位置',
                'label'=> '排序位置',
                'default' => 0,
            ),
        'addtime'=>array(
            'type' => 'time',
            'label' => '更改时间',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
        ),
        'last_modified' => array(
            'label' => '更新时间',
            'type' => 'last_modify',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
        ),
        'relation_path' =>
        array (
            'type' => 'text',
            'width'=>300,
            'editable' => false,
            'comment' => '区域路径',
        ),
    ),
    'index' =>
  array (
  ),
  'comment' => '门店老编码和新编码关联表',
);
