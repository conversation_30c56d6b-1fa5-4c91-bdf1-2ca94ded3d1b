<?php
$db['material_unique'] = array(
    'columns' => array(
        'id' => array(
            'type' => 'int unsigned',
            'required' => true,
            'hidden' => true,
            'editable' => false,
            'pkey' => true,
            'extra' => 'auto_increment',
        ),
        'store_id' => array(
            'type' => 'table:store@o2o',
            'label' => '所属门店',
            'editable' => false,
        ),
        'store_bn' => array(
            'type' => 'varchar(20)',
            'required' => true,
            'label' => '门店编码',
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        'org_id' => array(
            'type' => 'table:organization@organization',
            'editable' => false,
            'label' => '运营组织',
            'in_list' => true,
            'default_in_list' => true,
        ),
        'unique_id' => array(
            'type' => 'varchar(64)',
            'label' => 'SPU CODE',
            'width' => 200,
            'in_list' => true,
            'default_in_list' => true,
            'order' => 4,
            'required' => true,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        'remark' => array(
            'type' => 'varchar(255)',
            'label' => '备注',
            'editable' => false,
            'width' => 150,
        ),
        'create_time' => array(
            'type' => 'time',
            'label' => '创建时间',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
        ),
        'sku_sync_status' => array(
            'type' => array(
                'wait' => '待同步',
                'succ' => '同步成功',
                'fail' => '同步失败',
                'part' => '部分成功'
            ),
            'label' => 'SKU同步状态',
            'width' => 120,
            'default' => 'wait',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 10,
            'filtertype' => 'yes',
            'filterdefault' => true,
        ),
        'sku_sync_time' => array(
            'type' => 'time',
            'label' => 'SKU同步时间',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
        ),
        'sku_sync_msg' => array(
            'type' => 'varchar(255)',
            'label' => 'SKU同步信息',
            'editable' => false,
            'width' => 150,
            'in_list' => true,
        ),
        'stock_sync_status' => array(
            'type' => array(
                'wait' => '待同步',
                'succ' => '同步成功',
                'fail' => '同步失败',
                'part' => '部分成功'
            ),
            'label' => '库存同步状态',
            'width' => 120,
            'default' => 'wait',
            'in_list' => true,
            'default_in_list' => true,
            'order' => 11,
            'filtertype' => 'yes',
            'filterdefault' => true,
        ),
        'stock_sync_time' => array(
            'type' => 'time',
            'label' => '库存同步时间',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
        ),
        'stock_sync_msg' => array(
            'type' => 'varchar(255)',
            'label' => '库存同步信息',
            'editable' => false,
            'width' => 150,
            'in_list' => true,
        ),
        'last_modified' => array(
            'label' => '更新时间',
            'type' => 'last_modify',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
        ),
        'source' => array(
            'type' => 'varchar(30)',
            'editable' => false,
            'label' => '来源',
            'width' => 90,
            'in_list' => true,
            'default' => 'local',
            'comment' => 'local:本地,sap:SAP,openapi:openapi',
        ),
        'retry_sku_num' => array(
            'type' => 'int',
            'default' => 0,
            'label' => '重试同步SKU次数',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'retry_sku_time' => array(
            'type' => 'time',
            'label' => '最后重试同步SKU时间',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'retry_stock_num' => array(
            'type' => 'int',
            'label' => '重试同步库存次数',
            'default' => 0,
            'width' => 130,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'retry_stock_time' => array(
            'type' => 'time',
            'label' => '最后重试同步库存时间',
            'width' => 130,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        )
    ),
    'index' => array(
        'ind_store_id' => array('columns' => array('store_id')),
        'ind_org_id' => array('columns' => array('org_id')),
        'ind_unique_id' => array('columns' => array('unique_id')),
        'ind_sku_sync_status' => array('columns' => array('sku_sync_status')),
        'ind_stock_sync_status' => array('columns' => array('stock_sync_status')),
    ),
    'comment' => 'SKU/库存同步唯一码表',
    'engine' => 'innodb',
);
