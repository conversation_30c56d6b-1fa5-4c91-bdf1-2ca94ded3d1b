<?PHP

/**
 * 发送预警邮件
 */
class material_autotask_task_sendemail
{
    protected $storeList = [];

    /**
     * @access public
     * @param void
     * @return void
     */
    public function process($params, &$error_msg = '')
    {
        # 业务类型
        $type = $params['bill_type'];
        $params = unserialize($params['params']);
        if (empty($params)) {
            $error_msg = '参数不能为空';
            return false;
        }

        switch ($type) {
            case 'stock_warn': // 库存100 - 全量库存预警
                kernel::single('material_kucun100')->sendEmail($params);
                break;
        }

        return true;
    }


}
