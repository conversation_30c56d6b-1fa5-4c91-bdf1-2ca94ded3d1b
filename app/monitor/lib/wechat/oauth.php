<?php
/**
 * 微信网页授权类
 * 实现获取用户openid和用户信息
 */
class monitor_wechat_oauth
{
    private $appId;
    private $appSecret;
    private $tokenUtil;
    
    public function __construct()
    {
        if (!defined('SCAN_APP_ID') || !defined('SCAN_APP_SECRET')) {
            throw new Exception('未配置微信APP_ID或APP_SECRET');
        }
        
        $this->appId = SCAN_APP_ID;
        $this->appSecret = SCAN_APP_SECRET;
        $this->tokenUtil = kernel::single('monitor_wechat_token');
    }
    
    /**
     * 获取授权URL
     * 
     * @param string $redirectUri 回调URI，需urlencode处理
     * @param string $scope 应用授权作用域 snsapi_base或snsapi_userinfo
     * @param string $state 重定向后会带上state参数
     * @return string 授权URL
     */
    public function getAuthUrl($redirectUri, $scope = 'snsapi_userinfo', $state = '')
    {
        $redirectUri = urlencode($redirectUri);
        return "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$this->appId}&redirect_uri={$redirectUri}&response_type=code&scope={$scope}&state={$state}#wechat_redirect";
    }
    
    /**
     * 通过code获取网页授权access_token和openid
     * 
     * @param string $code 授权code
     * @return array|bool 成功返回授权信息，失败返回false
     */
    public function getAccessTokenByCode($code)
    {
        $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$this->appId}&secret={$this->appSecret}&code={$code}&grant_type=authorization_code";
        
        $response = $this->tokenUtil->httpRequest($url);
        $data = json_decode($response, true);
        
        if (isset($data['errcode']) && $data['errcode'] != 0) {
            kernel::log('getAccessTokenByCode error: ' . $response);
            return false;
        }
        
        return $data;
    }
    
    /**
     * 获取用户信息
     * 
     * @param string $accessToken 网页授权接口调用凭证
     * @param string $openid 用户的唯一标识
     * @return array|bool 成功返回用户信息，失败返回false
     */
    public function getUserInfo($accessToken, $openid, $type = 'snsapi_userinfo')
    {
        if ($type == 'snsapi_userinfo') {
            $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$accessToken}&openid={$openid}&lang=zh_CN";
        } else {
            $access_token = $this->tokenUtil->getAccessToken();
            $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$access_token}&openid={$openid}&lang=zh_CN";
        }
        
        $response = $this->tokenUtil->httpRequest($url);
        $data = json_decode($response, true);
        
        if (isset($data['errcode']) && $data['errcode'] != 0) {
            kernel::log('getUserInfo error: ' . $response);
            return false;
        }
        
        return $data;
    }
    
    /**
     * 将微信用户与账号关联并保存到session
     * 
     * @param array $userInfo 用户信息
     * @param string $accountId 账号ID
     * @param array $stores 关联的门店列表
     * @return bool 成功返回true，失败返回false
     */
    public function saveUserInfo($userInfo)
    {
        $accountId = kernel::single('desktop_user')->get_id();

        if (empty($userInfo) || empty($userInfo['openid'])) {
            return false;
        }
        
        $wechatUserModel = app::get('monitor')->model('wechat_users');

        // 检查openid是否已存在
        $existingUser = $wechatUserModel->dump(['openid' => $userInfo['openid']], '*');
        
        // 如果已存在，则更新用户信息
        if ($existingUser) {
            $updateData = [
                #'wap_uname' => $_SESSION['wap_uname'] ? $_SESSION['wap_uname'] : $userInfo['nickname'],
                'remark' => $userInfo['remark'] ? $userInfo['remark'] : $userInfo['nickname'],
                'update_time' => time()
            ];
            if (empty($updateData['wap_uname'])) {
                unset($updateData['wap_uname']);
            }
            if (empty($updateData['remark'])) {
                unset($updateData['remark']);
            }
            $wechatUserModel->update($updateData, ['openid' => $userInfo['openid'],'user_id' => $accountId]);
            return true;
        }
        
        // 保存用户信息到kvstore，使用openid作为键
        $userData = [
            'openid' => $userInfo['openid'],
            'wap_uname' => $_SESSION['wap_uname'] ? $_SESSION['wap_uname'] : $userInfo['nickname'],
            'remark' => $userInfo['remark'] ? $userInfo['remark'] : $userInfo['nickname'],
            'user_id' => $accountId,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        $wechatUserModel->insert($userData);
        return true;
    }
} 