<?php

/**
 * 配送超时微信通知处理类
 * 
 * 用于处理下单超过指定时间未发货订单的微信消息通知
 * 支持商城平台实时提醒功能
 */
class monitor_wechat_event_deliverytimeout
{

    public $template_code = 'order_delivery_timeout';

    /**
     * 发送配送超时通知
     * 
     * @param int $hour 超时小时数，默认48小时
     * @param string &$msg 返回消息
     * @param string $shop_type 店铺类型，为空时处理所有类型
     * @return bool 发送结果
     */
    public function send($hour = 48, &$msg = '', $shop_type = '')
    {
        // 参数验证
        if (!is_numeric($hour) || $hour < 0) {
            $msg = 'Delivery timeout notification: Invalid hour parameter';
            return false;
        }
        
        // 只查询4月20号以后的数据，测试环境历史未发货的数据太多，可能导致发送消息模版太多，临时进行限制
        // 正式环境没有只查询4月20号以后的数据 之前还未发货的订单，因此不影响 

        // 获取未发货且下单超过48小时的订单
        $db = kernel::database();
        
        // 构建店铺类型过滤条件
        $shop_type_condition = '';
        if (!empty($shop_type)) {
            $shop_type_condition = " AND o.shop_type = '" . addslashes($shop_type) . "'";
        }
        
        // 商城平台实时提醒逻辑
        $is_mall_platform = in_array($shop_type, array('ecos.ecshopx', 'ecshopx'));
        if ($is_mall_platform) {
            // 商城平台实时提醒：查询所有未发货订单
            $sql = "SELECT d.delivery_bn,d.branch_id,o.order_id, o.order_bn, o.createtime, o.pay_status, o.ship_status 
                FROM sdb_wap_delivery d
                LEFT JOIN sdb_ome_orders o ON d.order_bn = o.order_bn
                WHERE o.ship_status in ('0','2') 
                AND d.status='0' and d.logi_status='0' and o.createtime > " . (time() - 23 * 3600) . $shop_type_condition . "
                ORDER BY o.createtime ASC";
        } elseif ($hour == 48) {
            $sql = "SELECT d.delivery_bn,d.branch_id,o.order_id, o.order_bn, o.createtime, o.pay_status, o.ship_status 
                FROM sdb_wap_delivery d
                LEFT JOIN sdb_ome_orders o ON d.order_bn = o.order_bn
                WHERE o.ship_status in ('0','2') 
                AND o.createtime <= " . (time() - $hour * 3600) . " AND d.status='0' and d.logi_status='0' and o.createtime > 1745078400" . $shop_type_condition . "
                ORDER BY o.createtime ASC";
        } elseif ($hour == 23) {
            $sql = "SELECT d.delivery_bn,d.branch_id,o.order_id, o.order_bn, o.createtime, o.pay_status, o.ship_status 
                FROM sdb_wap_delivery d
                LEFT JOIN sdb_ome_orders o ON d.order_bn = o.order_bn
                WHERE o.ship_status in ('0','2') 
                AND o.createtime < " . (time() - $hour * 3600) . " AND d.status='0' and d.logi_status='0' and o.createtime > " . (time() - 48 * 3600) . $shop_type_condition . " ORDER BY o.createtime ASC";
        }
        
        $orders = $db->select($sql);
        
        if (empty($orders)) {
            $msg = 'Delivery timeout notification: No orders found that are unshipped for more than 48 hours';
            return true;
        }
        
        $messageObj = kernel::single('monitor_wechat_message');
        $count = 0;
        
        foreach ($orders as $order) {
            // 检查是否已经发送过通知
            $wechatNotifyModel = app::get('monitor')->model('wechat_notify');

            // 48小时是否提醒过
            $existingNotify = $wechatNotifyModel->dump(['related_id' => $order['delivery_bn'], 'template_code' => $this->template_code], 'notify_id');
            if (!empty($existingNotify)) {
                kernel::log('Delivery timeout notification: Already sent for delivery_bn: ' . $order['delivery_bn'] . ', order_bn: ' . $order['order_bn']);
                continue; // 已经发送过，跳过当前订单
            }

            // 商城平台实时提醒是否提醒过
            if ($is_mall_platform) {
                $existingNotify = $wechatNotifyModel->dump(['related_id' => $order['delivery_bn'].'_realtime', 'template_code' => $this->template_code], 'notify_id');
                if (!empty($existingNotify)) {
                    kernel::log('Delivery timeout notification: Already sent for delivery_bn: ' . $order['delivery_bn'] . ', order_bn: ' . $order['order_bn']);
                    continue; // 已经发送过，跳过当前订单
                }
            }

            // 23小时是否提醒过
            if ($hour == 23) {
                $existingNotify = $wechatNotifyModel->dump(['related_id' => $order['delivery_bn'] . '_23', 'template_code' => $this->template_code], 'notify_id');
                if (!empty($existingNotify)) {
                    kernel::log('Delivery timeout notification: Already sent for delivery_bn: ' . $order['delivery_bn'] . ', order_bn: ' . $order['order_bn']);
                    continue; // 已经发送过，跳过当前订单
                }
            }

            $openIds = $messageObj->getOpenIdByBranchId($order['branch_id']);

            //{"character_string1":"订单号","time3":"下单时间","time6":"超时时间","time10":"订单时长"}
            $data = array(
                'character_string1' => $messageObj->validateTemplateParam($order['order_bn'], 'character_string'),
                'time3' => date('Y-m-d H:i:s', $order['createtime']),
            );

            if ($is_mall_platform) {
                // 商城平台实时提醒：显示当前时间作为提醒时间
                $data['time6'] = date('Y-m-d H:i:s', time());
                $data['time10'] = "00:00";
            } elseif ($hour == 23) {
                $data['time6'] = date('Y-m-d H:i:s', $order['createtime'] + 23 * 3600);
                $data['time10'] = "23:00";
            } elseif ($hour == 48) {
                $data['time6'] = date('Y-m-d H:i:s', $order['createtime'] + 48 * 3600);
                $data['time10'] = "48:00";
            }
            
            // 发送微信通知
            $url = kernel::base_url(true).'/index.php/wap/order/index';

            if ($is_mall_platform) {
                $related_id = $order['delivery_bn'].'_realtime';
            } elseif ($hour == 23) {
                $related_id = $order['delivery_bn'] . '_23';
            } else {
                $related_id = $order['delivery_bn'];
            }

            $result = $messageObj->addMessage($this->template_code, $openIds, $data, $url, $related_id);
            
            if ($result) {
                $count++;
                kernel::log('Delivery timeout notification: Sent for delivery_bn: ' . $order['delivery_bn'] . ', order_bn: ' . $order['order_bn']);
            } else {
                kernel::log('Delivery timeout notification: Failed to send for delivery_bn: ' . $order['delivery_bn'] . ', order_bn: ' . $order['order_bn']);
            }
        }

        $msg = 'Delivery timeout notification: Total ' . $count . ' notifications sent';
        return true;
    }
}
