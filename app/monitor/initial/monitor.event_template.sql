INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_bill_fail_warning', 'WMS单据请求失败报警', 'wms_bill_fail_warning', 'email', 'WMS单据请求失败报警
    >业务：<font color=\"warning\">{title}</font>
    >单据：<font color=\"warning\">{bill_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-08-13 00:00:00', '2024-08-13 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_consign_notify', 'WMS发货失败报警', 'wms_delivery_consign', 'email', 'WMS发货失败报警
    >发货单：<font color=\"warning\">{delivery_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_reship_notify', 'WMS退货失败报警', 'wms_reship_finish', 'email', 'WMS退货失败报警
    >退货单：<font color=\"warning\">{reship_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_stockchange_notify', 'WMS异动失败报警', 'wms_stock_change', 'email', 'WMS异动失败报警
    >单据：<font color=\"warning\">{order_code}</font>
    >类型：<font color=\"warning\">{order_type}</font>
    >批次：<font color=\"warning\">{batch_code}</font>
    >仓库：<font color=\"warning\">{warehouse}</font>
    >物料：<font color=\"warning\">{product_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_stockin_notify', 'WMS入库失败报警', 'wms_stockin_finish', 'email', 'WMS入库失败报警
    >入库单：<font color=\"warning\">{io_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_stockout_notify', 'WMS出库失败报警', 'wms_stockout_finish', 'email', 'WMS出库失败报警
    >入库单：<font color=\"warning\">{io_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_stockprocess_notify', 'WMS加工单确认失败报警', 'wms_stockprocess_confirm', 'email', 'WMS加工单确认失败报警
    >加工单：<font color=\"warning\">{mp_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('wms_transferorder_notify', 'WMS移库失败报警', 'wms_transferorder_finish', 'email', 'WMS移库失败报警
    >移库单：<font color=\"warning\">{stockdump_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('pos_stock_notify', 'POS库存同步通知', 'pos_stock_sync', 'email', 'POS库存同步失败通知
    >门店：<font color=\"warning\">{store_bn}</font>
    >仓库：<font color=\"warning\">{branch_bn}</font>
    >PageNo：<font color=\"warning\">{page_no}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('pos_o2oundelivery_notify', 'POS现货订单未发货报警', 'pos_o2oundelivery', 'email', 'POS现货订单未发货报警
>现货未发货订单号为：<font color=\"warning\">{order_bns}</font>', '1', 'system', '2024-03-07 00:00:00',
        '2024-03-07 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('process_undelivery_notify', '未发货订单通知', 'process_undelivery', 'email', '{content}', '1', 'system',
        '2024-03-07 00:00:00', '2024-03-07 00:00:00');


INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('under_safty_inventory_notify', '低于安全库存报警', 'under_safty_inventory', 'email',
        '<{仓库名称：<font color=\"warning\">{branch_name}</font>，商品编码：<font color=\"warning\">{bn}</font>，商品名称：<font color=\"warning\">{goods_name}</font>，库存数量：<font color=\"warning\">{store}</font>，安全库存：<font color=\"warning\">{safe_store}</font>}>',
        '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('stock_diff_alarm', '实物库存差异报警', 'stock_diff_alarm', 'email', '实物库存差异报警
    >日期：<font color=\"warning\">{stock_date}</font>
    >渠道：<font color=\"warning\">{channel_bn}</font>
    >仓库：<font color=\"warning\">{warehouse_code}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('stock_sync_notify', '平台库存同步失败报警', 'stock_sync', 'email', '平台库存同步失败报警
    >日期：<font color=\"warning\">{stock_date}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('system_message_notify', '系统消息通知', 'system_message', 'email', '系统消息通知
    >消息内容：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-03-07 00:00:00', '2024-03-07 00:00:00');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`,
                                         `source`, `at_time`, `up_time`)
VALUES ('invoice_result_error_notify', '发票处理失败报警', 'invoice_result_error', 'workwx', '发票处理失败报警
    >发票操作类型：<font color=\"warning\">{invoice_type}</font>
    >订单号：<font color=\"warning\">{order_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2024-05-08 10:22:23', '2024-05-08 10:22:23');
INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('delivery_cancel_fail_notify', '发货单取消失败报警', 'delivery_cancel_fail', 'email', '发货单取消失败报警
    >发货单：<font color=\"warning\">{delivery_bn}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('interception_logi_fail_warning', '物流拦截失败预警', 'interception_logi_fail', 'email', '物流拦截失败预警
    >发货单：<font color=\"warning\">{delivery_bn}</font>
    >订单号：<font color=\"warning\">{order_bn}</font>
    >售后申请单：<font color=\"warning\">{return_bn}</font>
    >物流单：<font color=\"warning\">{logi_no}</font>
    >错误信息：<font color=\"warning\">{errmsg}</font>', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('order_delivery_timer_warning', '未发货订单定时预警', 'order_delivery_timer', 'email', '{content}', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('order_consign_fail_warning', '发货回传店铺失败预警', 'order_consign_fail', 'email', '{content}', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('order_refund_timer_warning', '未发货仅退款订单定时预警', 'order_refund_timer', 'email', '{content}', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('receive_order_warning', '订单接单异常', 'receive_order_warning', 'email', '{content}', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('interception_fail_while_warning', '物流拦截失败轮循预警', 'interception_fail_while', 'email', '物流拦截失败轮循预警
    >以下订单一直拦截失败：<br><font color=\"warning\">{delivery_bn}</font><br>
    >请前往 单据报表->拦截单据 里面查看', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('refunded_nostock_timer_warning', '已退款未入库定时预警', 'refunded_nostock_timer', 'email', '{content}', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('stored_norefund_timer_warning', '已入库未退款定时预警', 'stored_norefund_timer', 'email', '{content}', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('ordertoesb_check_timer_warning', '未推送ESB数据检查', 'ordertoesb_check_timer', 'email', '{content}', '1', 'system', '2025-04-01 00:00:00', '2025-04-01 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('h5_confirm_delivery_warning', '门店发货库存不足预警', 'h5_confirm_delivery_warning', 'email', '{content}', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('order_receive_fail', '失败订单邮件预警', 'order_receive_fail', 'email', '{content}', '1', 'system', '2025-04-17 00:00:00', '2025-04-17 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('retry_sync_kucun100', '重试获取库存100的商品和库存接口', 'retry_sync_kucun100', 'email', '{content}', '1', 'system', '2025-04-01 00:00:00', '2025-04-01 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('invoice_fail_warning', '开票失败拦截预警', 'invoice_fail_warning', 'email', '开票失败拦截预警
    >错误信息：<font color=\"warning\">{sync_msg}</font>', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('store_update_email', '门店店铺编码更换', 'store_update_email', 'email', '{content}', '1', 'system', '2025-04-01 00:00:00', '2025-04-01 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('interception_succ_cancel_warning', '物流拦截成功后取消退款申请预警', 'interception_succ_cancel', 'email', '物流拦截成功后取消退款申请预警
    >以下订单物流拦截成功后取消退款申请：<br><font color=\"warning\">{return_bn}</font><br>', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');

INSERT INTO `sdb_monitor_event_template`(`template_bn`, `template_name`, `event_type`, `send_type`, `content`, `status`, `source`, `at_time`, `up_time`)
VALUES ('storereceivewarning_warning', '门店未开启接单和配置接单时间预警', 'storereceivewarning', 'email', '门店未开启接单和配置接单时间预警
    <br><font color=\"warning\">{content}</font><br>', '1', 'system', '2025-02-16 00:00:00', '2025-02-16 00:00:00');
