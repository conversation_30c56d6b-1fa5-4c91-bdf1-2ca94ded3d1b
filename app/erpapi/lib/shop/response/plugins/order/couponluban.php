<?php

/**
 * 抖音平台原始优惠明细数据
 *
 * @access public
 * <AUTHOR>
 * @date  2021-07-01
 */
class erpapi_shop_response_plugins_order_couponluban extends erpapi_shop_response_plugins_order_abstract
{
    public function convert(erpapi_shop_response_abstract $platform)
    {
        // 更新的时候
        if ($platform->_tgOrder) {
            return array();
        }

        $coupon = array(
            'promotion_detail' => array(),
            'objects_coupon_data' => array(),
            'order_promotion' => [
                'promotion_detail' => $platform->_ordersdf['extend_field']['promotion_detail'],  // 订单行项目优惠信息
                'allowance_amount' => 0,    // 购物补贴抵扣金额
                'user_balance_amount' => 0, // 余额抵扣金额
                'gold_coin_amount' => 0,    // 金币抵扣金额
            ]
        );
        $platform_discount_detail = $coupon['order_promotion']['promotion_detail']['platform_discount_detail'] ?? [];
        # 购物补贴抵扣金额
        if (isset($platform_discount_detail['allowance_amount']) && floatval($platform_discount_detail['allowance_amount']) > 0) {
            $coupon['order_promotion']['allowance_amount'] = $platform_discount_detail['allowance_amount'];
        }
        # 余额抵扣金额
        if (isset($platform_discount_detail['user_balance_amount']) && floatval($platform_discount_detail['user_balance_amount']) > 0) {
            $coupon['order_promotion']['user_balance_amount'] = $platform_discount_detail['user_balance_amount'];
        }
        # 金币抵扣金额
        if (isset($platform_discount_detail['gold_coin_amount']) && floatval($platform_discount_detail['gold_coin_amount']) > 0) {
            $coupon['order_promotion']['gold_coin_amount'] = $platform_discount_detail['gold_coin_amount'];
        }

        $object_total_amount = 0;
        $split_objects = [];

        foreach ($platform->_ordersdf['order_objects'] as $object) {
            # 记录object总金额
            $object_total_amount = bcadd($object_total_amount, $object['amount'], 2);

            $objectData = [
                'order_bn' => $platform->_ordersdf['order_bn'],
                'num' => $object['quantity'],
                'material_name' => $object['name'],
                'material_bn' => $object['bn'],
                'oid' => $object['oid'],
                'create_time' => time(),
                'shop_id' => $platform->_ordersdf['shop_id'],
                'shop_type' => $platform->_ordersdf['shop_type'],
                'source' => 'push',
                'org_id' => $platform->_ordersdf['org_id'],
            ];
            # 支付优惠明细
            $addon = [
                'promotion_detail' => $object['extend_item_list']['promotion_detail'],
                'campaign_info' => []
            ];
            # 营销活动明细
            if (!empty($platform->_ordersdf['extend_field']['campaign_info'])) {
                $addon['campaign_info'] = $platform->_ordersdf['extend_field']['campaign_info'][$object['oid']]['campaign_info'];
            }
            $objectData['addon'] = serialize($addon);
            $coupon['objects_coupon_data'][] = $objectData;

            # 平台优惠
            if (!empty($addon['promotion_detail']['platform_discount_detail'])) {
                $platform_coupon_list = $this->_format_platform_coupon($addon['promotion_detail']['platform_discount_detail']);
            }
            # 商家优惠
            if (!empty($addon['promotion_detail']['shop_discount_detail'])) {
                $shop_coupon_list = $this->_format_platform_coupon($addon['promotion_detail']['shop_discount_detail']);
            }
            # 达人优惠
            if (!empty($addon['promotion_detail']['kol_discount_detail'])) {
                $kol_coupon_list = $this->_format_platform_coupon($addon['promotion_detail']['kol_discount_detail']);
            }
            # 营销优惠
            if (!empty($platform->_ordersdf['extend_field']['campaign_info'])) {
                $campaign_list = $this->_format_campaign_coupon($platform->_ordersdf['extend_field']['campaign_info'], $object['oid']);
            }

            # 其他字段信息
            $other_fields = [
                'oid' => $object['oid'],
                'material_bn' => $object['bn'],
                'num' => $object['quantity'],
                'create_time' => time(),
                'source' => 'push'
            ];
            if (!empty($platform_coupon_list)) {
                foreach ($platform_coupon_list as $item) {
                    $item_fields = [
                        'platform_type' => 'platform_discount'
                    ];
                    # 合并数据
                    $new_item = array_merge($item, $other_fields, $item_fields);
                    $coupon['promotion_detail'][] = $new_item;
                }
            }
            if (!empty($shop_coupon_list)) {
                foreach ($shop_coupon_list as $item) {
                    $item_fields = [
                        'platform_type' => 'shop_discount'
                    ];
                    # 合并数据
                    $new_item = array_merge($item, $other_fields, $item_fields);
                    $coupon['promotion_detail'][] = $new_item;
                }
            }
            if (!empty($kol_coupon_list)) {
                foreach ($kol_coupon_list as $item) {
                    $item_fields = [
                        'platform_type' => 'kol_discount'
                    ];
                    # 合并数据
                    $new_item = array_merge($item, $other_fields, $item_fields);
                    $coupon['promotion_detail'][] = $new_item;
                }
            }
            # 营销活动基础信息
            if (!empty($campaign_list)) {
                foreach ($campaign_list as $item) {
                    $item_fields = [
                        'platform_type' => 'campaign_discount'
                    ];
                    # 合并数据
                    $new_item = array_merge($item, $other_fields, $item_fields);
                    $coupon['promotion_detail'][] = $new_item;
                }
            }
        }

        # 如果订单明细中不存在【购物补贴、余额抵扣、金币抵扣】则使用订单行优惠进行分摊
        $couponData = $this->_format_coupon_detail($coupon['promotion_detail'], 'platform_discount');
        # 计算订单行和明细行金额不一致的字段名
        $split_field = $this->_check_field_amount($coupon['order_promotion'], $couponData);

        # 计算分摊
        if (!empty($split_field)) {
            foreach ($split_field as $field) {
                # 使用oid计算分摊
                $splitData = $this->split_objects($platform->_ordersdf['order_objects'], $object_total_amount, $coupon['order_promotion'][$field], $field);
                # 因为存在一个订单多SKU的情况，因此需要根据oid逐条计算分摊
                foreach ($splitData as $oid => $item) {
                    $split_objects[$oid][$field] = $item[$field];
                }
            }
        }

        # 组装coupon参数
        if (!empty($split_objects)) {
            foreach ($platform->_ordersdf['order_objects'] as $object) {
                # 其他字段信息
                $other_fields = [
                    'oid' => $object['oid'],
                    'material_bn' => $object['bn'],
                    'num' => $object['quantity'],
                    'create_time' => time(),
                    'source' => 'push'
                ];

                # 重新获取优惠列表
                $platform_coupon_list = $this->_format_platform_coupon($split_objects[$object['oid']] ?? []);
                if (empty($platform_coupon_list)) {
                    continue;
                }

                foreach ($platform_coupon_list as $item) {
                    $item_fields = [
                        'platform_type' => 'platform_discount'
                    ];
                    # 合并数据
                    $new_item = array_merge($item, $other_fields, $item_fields);
                    $coupon['promotion_detail'][] = $new_item;
                }
            }
        }

        return $coupon;
    }

    private function _check_field_amount($promotion, $couponData)
    {
        $result = [];

        # 购物补贴抵扣金额
        $allowance = floatval($promotion['allowance_amount']) > 0 ? bcdiv($promotion['allowance_amount'], 100, 2) : 0;
        # 余额抵扣金额
        $user_balance = floatval($promotion['user_balance_amount']) > 10 ? bcdiv($promotion['user_balance_amount'], 100, 2) : 0;
        # 金币抵扣金额
        $gold_coin = floatval($promotion['gold_coin_amount']) > 0 ? bcdiv($promotion['gold_coin_amount'], 100, 2) : 0;

        if (bccomp($allowance, $couponData['allowance'] ?? 0, 2) != 0) {
            $result[] = 'allowance_amount';
        }
        if (bccomp($user_balance, $couponData['user_balance'] ?? 0, 2) != 0) {
            $result[] = 'user_balance_amount';
        }
        if (bccomp($gold_coin, $couponData['gold_coin'] ?? 0, 2) != 0) {
            $result[] = 'gold_coin_amount';
        }
        return $result;
    }

    /**
     * 分摊逻辑
     * @param $objects
     * @param $total_amount
     * @return void
     */
    private function split_objects($objects, $total_amount, $split_amount, $field)
    {
        $split_count = count($objects);
        $last_amount = $split_amount;
        # 计算分摊比例
        $split_rate = bcdiv($split_amount, $total_amount, 6);
        $result = [];

        foreach ($objects as $k => $object) {
            if ($k + 1 == $split_count) {
                $tmp_data = [
                    $field => $last_amount
                ];
            } else {
                $obj_amount = bcmul($object['amount'], $split_rate, 2);
                $tmp_data = [
                    $field => $obj_amount
                ];
                $last_amount = bcsub($last_amount, $obj_amount, 2);
            }
            $result[$object['oid']] = $tmp_data;
        }
        return $result;
    }

    /**
     * 优惠列表数据格式处理
     * @param $data
     * @return mixed
     */
    private function _format_coupon_detail($data, $type = null)
    {
        if (empty($data)) {
            return [];
        }

        $result = [];
        foreach ($data as $item) {
            if (empty($result[$item['platform_type']][$item['type']])) {
                $result[$item['platform_type']][$item['type']] = $item['coupon_amount'];
            } else {
                $result[$item['platform_type']][$item['type']] += $item['coupon_amount'];
            }
        }
        return $result[$type] ?? [];
    }

    /**
     * 订单完成后处理
     *
     * @return void
     * <AUTHOR>
    public function postCreate($order_id, $coupon_data)
    {
        //记录优惠明细
        $coupon = $coupon_data['promotion_detail'];
        if (!empty($coupon)) {
            $couponObj = app::get('ome')->model('order_coupon_luban');
            foreach ($coupon as $key => $value) {
                $coupon[$key]['order_id'] = $order_id;
                $couponObj->save($coupon[$key]);
            }
        }

        $coupon_sku = $coupon_data['objects_coupon_data'];
        if ($coupon_sku) {
            //汇总优惠明细
            $objectsCouponMdl = app::get('ome')->model('order_objects_coupon');
            foreach ($coupon_sku as $key => $value) {
                $coupon_sku[$key]['order_id'] = $order_id;
                $objectsCouponMdl->save($coupon_sku[$key]);
            }
        }
    }

    public function postUpdate($order_id, $coupon_data)
    {
        //记录优惠明细
        $coupon = $coupon_data['promotion_detail'];
        $couponObj = app::get('ome')->model('order_coupon_luban');
        foreach ($coupon as $key => $value) {
            $coupon[$key]['order_id'] = $order_id;
        }
        if (!$couponObj->db_dump(array('order_id' => $order_id))) {
            $sql = ome_func::get_insert_sql($couponObj, $coupon);
            kernel::database()->exec($sql);
        }

        //汇总优惠明细
        $coupon_sku = $coupon_data['objects_coupon_data'];
        $objectsCouponMdl = app::get('ome')->model('order_objects_coupon');
        foreach ($coupon_sku as $key => $value) {
            $coupon_sku[$key]['order_id'] = $order_id;
        }
        if (!$objectsCouponMdl->db_dump(array('order_id' => $order_id))) {
            $sql = ome_func::get_insert_sql($objectsCouponMdl, $coupon_sku);
            kernel::database()->exec($sql);
        }
    }

    /**
     * 获取优惠数据
     * @param $params
     * @return void
     */
    private function _format_platform_coupon($params)
    {
        if (empty($params)) {
            return [];
        }

        $result = [];
        # 优惠券
        if (!empty($params['coupon_info'])) {
            foreach ($params['coupon_info'] as $item) {
                $coupon = [
                    'type' => 'coupon',
                    'coupon_id' => $item['coupon_id'],
                    'coupon_name' => $item['coupon_name'],
                    'coupon_type' => $item['coupon_type'],
                    'coupon_amount' => bcdiv($item['coupon_amount'], 100, 2),
                ];
                if (!empty($item['coupon_meta_id'])) {
                    $coupon['coupon_meta_id'] = $item['coupon_meta_id'];
                }
                # 成本分摊
                if (isset($item['share_discount_cost'])) {
                    $share_discount_cost = $item['share_discount_cost'];
                    if (isset($share_discount_cost['platform_cost']) && floatval($share_discount_cost['platform_cost']) > 0) {
                        $coupon['platform_cost'] = bcdiv($share_discount_cost['platform_cost'], 100, 2);
                    }
                    if (isset($share_discount_cost['shop_cost']) && floatval($share_discount_cost['shop_cost']) > 0) {
                        $coupon['shop_cost'] = bcdiv($share_discount_cost['shop_cost'], 100, 2);
                    }
                    if (isset($share_discount_cost['author_cost']) && floatval($share_discount_cost['author_cost']) > 0) {
                        $coupon['author_cost'] = bcdiv($share_discount_cost['author_cost'], 100, 2);
                    }
                }
                # 营销优惠扩展字段
                if (!empty($item['extra_map'])) {
                    $coupon['extend'] = serialize($item['extra_map']);
                }
                $result[] = $coupon;
            }
        }

        # 活动信息
        if (!empty($params['full_discount_info'])) {
            foreach ($params['full_discount_info'] as $item) {
                $coupon = [
                    'type' => 'discount',
                    'sub_type' => $item['campaign_sub_type'],
                    'coupon_id' => $item['campaign_id'],
                    'coupon_name' => $item['campaign_name'],
                    'coupon_type' => $item['campaign_type'],
                    'coupon_meta_id' => $item['coupon_meta_id'],
                    'coupon_amount' => bcdiv($item['campaign_amount'], 100, 2),
                ];
                # 成本分摊
                if (isset($item['share_discount_cost'])) {
                    $share_discount_cost = $item['share_discount_cost'];
                    if (isset($share_discount_cost['platform_cost']) && floatval($share_discount_cost['platform_cost']) > 0) {
                        $coupon['platform_cost'] = bcdiv($share_discount_cost['platform_cost'], 100, 2);
                    }
                    if (isset($share_discount_cost['shop_cost']) && floatval($share_discount_cost['shop_cost']) > 0) {
                        $coupon['shop_cost'] = bcdiv($share_discount_cost['shop_cost'], 100, 2);
                    }
                    if (isset($share_discount_cost['author_cost']) && floatval($share_discount_cost['author_cost']) > 0) {
                        $coupon['author_cost'] = bcdiv($share_discount_cost['author_cost'], 100, 2);
                    }
                }
                # 营销优惠扩展字段
                if (!empty($item['extra_map'])) {
                    $coupon['extend'] = serialize($item['extra_map']);
                }
                $result[] = $coupon;
            }
        }

        # 红包信息
        if (!empty($params['redpack_info'])) {
            foreach ($params['redpack_info'] as $item) {
                $coupon = [
                    'type' => 'redpack',
                    'coupon_id' => $item['redpack_trans_id'],
                    'coupon_name' => '红包',
                    'coupon_amount' => bcdiv($item['redpack_amount'], 100, 2)
                ];
                # 成本分摊
                if (isset($item['share_discount_cost'])) {
                    $share_discount_cost = $item['share_discount_cost'];
                    if (isset($share_discount_cost['platform_cost']) && floatval($share_discount_cost['platform_cost']) > 0) {
                        $coupon['platform_cost'] = bcdiv($share_discount_cost['platform_cost'], 100, 2);
                    }
                    if (isset($share_discount_cost['shop_cost']) && floatval($share_discount_cost['shop_cost']) > 0) {
                        $coupon['shop_cost'] = bcdiv($share_discount_cost['shop_cost'], 100, 2);
                    }
                    if (isset($share_discount_cost['author_cost']) && floatval($share_discount_cost['author_cost']) > 0) {
                        $coupon['author_cost'] = bcdiv($share_discount_cost['author_cost'], 100, 2);
                    }
                }
                # 营销优惠扩展字段
                if (!empty($item['extra_map'])) {
                    $coupon['extend'] = serialize($item['extra_map']);
                }
                $result[] = $coupon;
            }
        }

        # 余额抵扣
        if (isset($params['user_balance_amount']) && floatval($params['user_balance_amount']) > 0) {
            $coupon = [
                'type' => 'user_balance',
                'coupon_name' => '余额抵扣',
                'coupon_amount' => bcdiv($params['user_balance_amount'], 100, 2)
            ];
            $result[] = $coupon;
        }

        # 购物补贴抵扣
        if (isset($params['allowance_amount']) && floatval($params['allowance_amount']) > 0) {
            $coupon = [
                'type' => 'allowance',
                'coupon_name' => '购物补贴抵扣',
                'coupon_amount' => bcdiv($params['allowance_amount'], 100, 2)
            ];
            $result[] = $coupon;
        }

        # 金币抵扣
        if (isset($params['gold_coin_amount']) && floatval($params['gold_coin_amount']) > 0) {
            $coupon = [
                'type' => 'gold_coin',
                'coupon_name' => '金币抵扣',
                'coupon_amount' => bcdiv($params['gold_coin_amount'], 100, 2)
            ];
            $result[] = $coupon;
        }

        return $result;
    }

    /**
     * 获取营销基础信息
     * @param $params
     * @param $oid
     * @return void
     */
    private function _format_campaign_coupon($params, $oid)
    {
        if (empty($params) || empty($oid) || empty($params[$oid])) {
            return false;
        }

        $result = [];
        # 营销基础信息
        $campaignInfo = $params[$oid];
        foreach ($campaignInfo['campaign_info'] as $item) {
            $coupon = [
                'type' => 'campaign',
                'sub_type' => $item['campaign_sub_type'],  // 活动子类型：0-店铺活动，1-平台活动
                'coupon_id' => $item['campaign_id'],
                'coupon_name' => $item['campaign_name'],
                'coupon_type' => $item['campaign_type'],
                'coupon_amount' => bcdiv($item['campaign_amount'], 100, 2)
            ];
            if (!empty($item['campaign_activity_id'])) {
                $coupon['coupon_meta_id'] = $item['campaign_activity_id'];
            }
            if (!empty($item['share_discount_cost']['platform_cost'])) {
                $coupon['platform_cost'] = bcdiv($item['share_discount_cost']['platform_cost'], 100, 2);
            }
            if (!empty($item['share_discount_cost']['shop_cost'])) {
                $coupon['shop_cost'] = bcdiv($item['share_discount_cost']['shop_cost'], 100, 2);
            }
            if (!empty($item['share_discount_cost']['author_cost'])) {
                $coupon['author_cost'] = bcdiv($item['share_discount_cost']['author_cost'], 100, 2);
            }
            if (!empty($item['extra_map'])) {
                $coupon['extend'] = serialize($item['extra_map']);
            }
            $result[] = $coupon;
        }
        return $result;
    }
}