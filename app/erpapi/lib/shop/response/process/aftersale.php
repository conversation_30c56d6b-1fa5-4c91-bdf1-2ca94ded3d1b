<?php
/**
 * @desc
 * @author: jintao
 * @since: 2016/7/21
 */
class erpapi_shop_response_process_aftersale
{

    public function add($params)
    {
     	if($params['action']=='update') {
            return $this->update_aftersale($params);
        }   
	    $items = $params['return_product_items'];
        unset($params['return_product_items']);
        $opInfo = kernel::single('ome_func')->get_system();
        $params['order_id'] = $params['order']['order_id'];
        $params['op_id'] = $opInfo['op_id'];
        $params['source'] = 'matrix';

        //判断7天无理由
        $storeInfo = [];
        foreach($items as $ik => $iv){
            //获取发货门店信息
            $storeInfo = kernel::single("o2o_store_material")->getBmDelivStore($iv['bn'], $params['order_id']);
        }
        if(empty($storeInfo)){//兼容自提订单自动发货，wap_delivery表无数据，查不到门店
            $ordInfo = app::get("ome")->model("orders")->dump($params['order_id'], "order_type,selfpickup_store_id");
            if($ordInfo['order_type'] == 'selfpickup' && $ordInfo['selfpickup_store_id']){
                $storeMdl = app::get("o2o")->model("store");
                $storeInfo = $storeMdl->dump($ordInfo['selfpickup_store_id'], "store_id,name,performance_type,store_bn,branch_id,status,auto_agree_return");
            }

        }

        //如果是退货退款 并且是门店履约 判断是否7天无理由
        if (isset($storeInfo['performance_type']) && $storeInfo['performance_type'] == 'store') {
            $params['belong_store_id'] = isset($storeInfo['store_id']) ? $storeInfo['store_id'] : 0;
            $params['belong_store_bn'] = isset($storeInfo['store_bn']) ? $storeInfo['store_bn'] : '';
            if ($params['content'] == '7天无理由退款') {
                $params['belong_type'] = 'store';
                $params['wait_customer_check'] = '0';
                $params['intercept_status'] = '0';
                $params['store_check_status'] = '0';
            } else {
                //非7天无理由，获取指定渠道，需要客服审核
                $shopIds = app::get('ome')->getConf('seven.return.shop');
                $shopIds = $shopIds ?: [];
                if (in_array($params['shop_id'], $shopIds)) {
                    $params['belong_type'] = 'customer';
                    $params['twentyfour_hours_check'] = '1';
                    $params['wait_customer_check'] = '1';
                    $params['store_check_status'] = '0';
                } else {
                    $params['belong_type'] = 'store';
                    $params['wait_customer_check'] = '0';
                    $params['intercept_status'] = '0';
                    $params['store_check_status'] = '0';
                }
            }
        }

        app::get('ome')->model('return_product')->create_return_product($params);
        if(empty($params['return_id'])) {
            return array('rsp'=>'fail', 'msg'=>'售后申请单新建失败');
        }
        
        //log
        $oOperation_log = app::get('ome')->model('operation_log');
        $oOperation_log->write_log('return@ome',$params['return_id'],'创建售后申请单');
        
        $returnItemModel = app::get('ome')->model('return_product_items');
        foreach($items as $item) {
            $item['return_id'] = $params['return_id'];
            $returnItemModel->insert($item);
        }
        $is_return_auto_receive =  app::get('ome')->getConf('ome.return.auto.receive');
        #检测是否开启售后自动审核 兼容ecos.ecshopx
        if($is_return_auto_receive == 'true' || ($params['node_type'] == 'ecos.ecshopx' && $storeInfo['auto_agree_return'] == 'true')){
            #检测是不是分销王(分销王没有换货，只有退货)
            if(in_array($params['node_type'],['shopex_b2b','ecos.ecshopx'])){
                #检测状态是小于3,才可以自动接受申请操作
                if( $params['status'] < 3){
                    #组装数据
                    $adata = array(
                        'choose_type_flag'=>'1',#退货单
                        'status'=>'3',#直接更新为已接受
                        'return_id'=>$params['return_id'],
                        'memo'=>'自动退货申请'
                    );
                    app::get('ome')->model('return_product')->tosave($adata,false);
                }
            }
        }
        //商城下来已接受申请的售后
        if($params['node_type'] == 'ecos.ecshopx' && $params['status'] == '3'){
            #组装数据
            $adata = array(
                'choose_type_flag'=>'1',#退货单
                'status'=>'3',#直接更新为已接受
                'return_id'=>$params['return_id'],
                'memo'=>'前端店铺已接受申请，OMS自动接受售后申请'
            );
            app::get('ome')->model('return_product')->tosave($adata,false);
        }

        if($params['table_additional']){

           $this->_dealTableAdditional($params['table_additional']);
        }
        return array('rsp'=>'succ', 'msg'=>'售后单接收成功');
    }

    public function statusUpdate($params) {
        $status = $params['status'];
        $tgReturnItems = $params['return_items'];
        $data = array(
            'status'    => $status,
            'return_id' => $params['return_id'],
        );
        $returnModel = app::get('ome')->model('return_product');
        if (in_array($status, array('2','3'))) {
            foreach ($tgReturnItems as $key => $item) {
                $data['item_id'][$key]          = $item['item_id'];
                $data['effective'][$item['bn']] =  $item['num'];
                $data['bn'][$item['bn']]        = $item['num'];
            }
            $data['choose_type_flag'] = 1;
            $returnModel->tosave($data);
        } elseif ($status == '4') {
            $totalmoney = 0;
            foreach ($tgReturnItems as $key => $item) {
                $data['branch_id'][$key]  = $item['branch_id'];
                $data['product_id'][$key] = $item['product_id'];
                $data['item_id'][$key]    = $item['item_id'];
                $data['effective'][$key]  = $item['num'];
                $data['name'][$key]       = $item['name'];
                $data['bn'][$key]         = $item['bn'];
                $data['deal'.$key]        = 1;
            }
            $data['totalmoney'] = $totalmoney;
            $data['tmoney']     = $totalmoney;
            $data['bmoney']     = 0;
            $data['memo']       = '';
            /*统计此次请求对应货号退货数量累加*/
            $can_refund = array();
            foreach($data['bn'] as $k=>$v){
                if(isset($can_refund[$v])){
                    $can_refund[$v]['num']++;
                }else{
                    $can_refund[$v]['num']=1;
                    $can_refund[$v]['effective'] = $data['effective'][$k];
                }
                if($can_refund[$v]['effective'] == 0){
                    return array('rsp'=>'fail', 'msg' => '货号为['.$v.']没有可申请量，请选择拒绝操作,订单号:'.$params['order_bn'].',售后申请单号:'.$params['return_bn']);
                }else if($can_refund[$v]['num'] > $can_refund[$v]['effective']){
                    return array('rsp'=>'fail', 'msg' => '货号为['.$v.']大于可申请量，请选择拒绝操作,订单号:'.$params['order_bn'].',售后申请单号:'.$params['return_bn']);
                }
            }
            $returnModel->saveinfo($data, true);
        } else {
            //如果是拒绝，查找有没有未处理的退货单，退货单也拒绝掉
            if($status == '5'){
                $reshipMdl = app::get("ome")->model("reship");
                $rFilter = array(
                    'return_id' => $params['return_id'],
                    'is_check|noequal' => '7',
                );
                $reshipInfo = $reshipMdl->dump($rFilter, "reship_id");
                if(isset($reshipInfo['reship_id']) && $reshipInfo['reship_id']){
                    $reshipMdl->update(array('is_check' => '5'), array("reship_id" => $reshipInfo['reship_id']));
                }
            }
            $returnModel->update(array('status'=>$status),array('return_id'=>$params['return_id']));
        }
        return array('rsp'=>'succ', 'msg'=>'售后申请单状态更新成功');
    }

    public function logisticsUpdate($params) {
        $processData = array_merge((array)$params['old_process_data'], (array)$params['process_data']);
        app::get('ome')->model('return_product')->update(array('process_data'=>serialize($processData)),array('return_id'=>$params['return_id']));
        #分销王的退货单，同步物流信息
        // if( in_array ($params['node_type'],ome_shop_type::shopex_shop_type()) ){
            $obj_reship = app::get('ome')->model('reship');
            $rs  = $obj_reship->count(array('return_id'=>$params['return_id']));
            if($rs > 0){
                $_data['return_logi_name'] = $params['process_data']['shipcompany'];
                $_data['return_logi_no'] = $params['process_data']['logino'];
                $obj_reship->update($_data,array('return_id'=>$params['return_id']));
    
                $reshipInfo = $obj_reship->db_dump(['return_id'=>$params['return_id']],'reship_id,shop_type');
                //退换货自动审批
                if($_data['return_logi_no'] && $reshipInfo && $reshipInfo['shop_type'] == 'ecos.ecshopx'){
                    kernel::single('ome_reship')->batch_reship_queue($reshipInfo['reship_id']);
                }
            }
        // }
        return array('rsp'=>'succ', 'msg'=>'物流信息更新成功');
    }

    private function _dealTableAdditional($tableAdditional) {
        if(empty($tableAdditional)) {
            return false;
        }

        $model = app::get('ome')->model($tableAdditional['model']);
        unset($tableAdditional['model']);

        $model->db_save($tableAdditional);
    }
    
    public function update_aftersale($params){
        //全民分销可以编辑，拒绝后可以重新开始
        if($params['status'] == '1'||$params['status'] == '5') {
            $oOperation_log = app::get('ome')->model('operation_log');//写日志
            unset($params['return_product_items']);
            unset($params['action']);
            $opInfo = kernel::single('ome_func')->get_system();
            $params['order_id'] = $params['order']['order_id'];
            $params['op_id'] = $opInfo['op_id'];
            $params['source'] = 'matrix';
            $rs = app::get('ome')->model('return_product')->update($params, array('return_id'=>$params['return_id']));
            if (is_bool($rs)) {
                return array('rsp' => 'fail', 'msg' => "更新售后申请单[{$params['return_bn']}]状态失败：可能是金额不一致");
            } else {
                $memo = '(退款金额、原因或版本变化)售后申请单更新为未审核';
                $oOperation_log->write_log('return@ome', $params['return_id'], $memo);
                return array('rsp' => 'succ', 'msg' => "更新售后申请单[{$params['return_bn']}]状态成功：{$params['status']},影响行数：" . $rs);
            }
        }
    }
}