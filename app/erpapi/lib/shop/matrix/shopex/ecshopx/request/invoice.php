<?php
/**
 * <AUTHOR> 2016/5/6
 * @describe 发票 相关请求接口类
 */

class erpapi_shop_matrix_shopex_ecshopx_request_invoice extends erpapi_shop_request_invoice {

    public function upload($sdf, $sync = false)
    {
        $invoice = $sdf['invoice'];
        //开票中 已开蓝 已开红 或者已经作废
        if (in_array($invoice['sync'], ['1', '3', '6']) || $invoice['is_status'] == '2') {
            return parent::upload($sdf, $sync);
        }
        return ['rsp' => 'fail', 'msg' => '不在对应状态中不同步!'];
    }

    /**
     * summary
     *
     * @return void
     * <AUTHOR>
    protected function getUploadParams($sdf)
    {
        $invoice    = $sdf['invoice'];
        $electronic = $sdf['electronic'];

        $params = array(
            'invoice_type'      => $electronic['billing_type'], # 发票类型 1-蓝票 2-红票                 必须
            'tid'               => $invoice['order_bn'], # 订单编号                    必须
            'order_type'        => $this->__channelObj->channel['node_type'],  # 订单类型                    必须
            'payee_register_no' => $invoice['tax_no'], # 销货方识别号（税号）        必须
            'payee_name'        => $invoice['payee_name'],  # 销货方公司名称              必须
            'payee_address'     => $invoice['address'],   # 销货方公司地址   
            'payee_phone'       => $invoice['telephone'], # 销货方电话
            'payee_bankname'    => $invoice['bank'],  # 销货方公司开户行
            'payee_bankaccount' => $invoice['bank_no'],   # 销货方公司银行账户
            'payee_operator'    => $invoice['payee_operator'],  # 开票人
            'payee_receiver'    => '',  # 收款人
            'invoice_title'     => $invoice['title'],   # 发票抬头                    必须 
            'taxfree_amount'    => round($invoice['amount'],2),  # 开票金额 两位小数w
            'invoice_time'      => date('Y-m-d H:i:s',$electronic['create_time']),    # 开票时间 yyyy-MM-dd         必须
            'ivc_content_type'  => '',    # 开票内容编号
            'ivc_content_name'  => '',    # 开票内容名称
            'invoice_code'      => $electronic['invoice_code'],    # 发票代码                    必须
            'invoice_no'        => $invoice['invoice_apply_bn'],  # 发票号码                    必须
            'invoice_memo'      => '',    # 发票备注
            'blue_invoice_code' => (string)$electronic['normal_invoice_code'],    # 原蓝票发票代码                开红票的时候必须传
            'blue_invoice_no'   => (string)$electronic['normal_invoice_no'],  # 原蓝票发票号码                开红票的时候必须传
            'pdf_info'          => $electronic['url'], # 发票PDF文件二进制流base64   必须
        );

        if($invoice['sync'] == '1'){
            //开蓝中
            $params['invoice_memo'] = '1';
        }else if($invoice['sync'] == '3'){
            //开蓝成功
            $params['invoice_memo'] = '2';
        }else if($invoice['is_status'] == '2' || $invoice['sync']=='6') {
            //已作废
            $params['invoice_memo'] = '3';
        }

        $electronic_obj = app::get('invoice')->model('order_electronic_items');
        //找开蓝和开红信息
        if($params['invoice_memo'] == '2'){
            $electronic_info = $electronic_obj->dump(['id'=>$invoice['id'],'billing_type'=>'1']);
            if($electronic_info){
                $params['blue_invoice_code'] = $electronic_info['invoice_code'];
                $params['blue_invoice_no'] = $electronic_info['invoice_no'];
                $params['invoice_time'] = date('Y-m-d H:i:s',$electronic_info['create_time']);
            }
        }else if($params['invoice_memo'] == '3'){
            $electronic_info = $electronic_obj->dump(['id'=>$invoice['id'],'billing_type'=>'2']);
            if($electronic_info){
                $params['blue_invoice_code'] = $electronic_info['invoice_code'];
                $params['blue_invoice_no'] = $electronic_info['invoice_no'];
                $params['invoice_time'] = date('Y-m-d H:i:s',$electronic_info['create_time']);
            }
        }


        if ($sdf['items']) {
            $items = array();
            foreach ($sdf['items'] as $value) {
                $items[] = array(
                    'item_no'              => '', # 货号
                    'item_name'            => $value['spmc'], # SKU商品名称
                    'num'                  => $value['spsl'], # 数量 
                    'price'                => round($value['spdj'],2), # 单价 
                    'spec'                 => '', # 规格 
                    'unit'                 => $value['dw'], # 单位 
                    'tax_rate'             => $value['sl'], # 税率 两位小数
                    'tax_categroy_code'    => $value['spbm'], # 税收分类编码
                    'is_tax_discount'      => $value['yhzcbs'], # 优惠政策标识 0-不使用 1-使用
                    'tax_discount_content' => $value['zzstsgl'], # 增值税特殊管理 当优惠政策标识为1时填写 
                    'zero_tax'             => $value['lslbs'], # 零税率标识 空-非零税率 0-出口退税 1-免税 2-不征收 3-普通零税率
                    'deductions'           => '', # 扣除额 两位小数
                    'imei'                 => '', # 商品IMEI码
                    'discount'             => 0, # 折扣
                    'freight'              => 0, # 运费
                );
            }
            $params['invoice_items'] = json_encode($items);
        }

        return $params;
    }

}
