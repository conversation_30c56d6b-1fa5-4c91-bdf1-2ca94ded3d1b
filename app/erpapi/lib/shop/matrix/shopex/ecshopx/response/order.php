<?php

class erpapi_shop_matrix_shopex_ecshopx_response_order extends erpapi_shop_matrix_shopex_response_order
{
    protected $_update_accept_dead_order = true;


    protected function _analysis()
    {
        parent::_analysis();
        if ($this->_ordersdf['shipping']['shipping_name'] == 'STORE_SELF_FETCH' || $this->_ordersdf['shipping']['shipping_name'] == 'STORE_TONGCHEN_EXPRESS') {
            $this->_setPlatformDelivery();
        }
        $this->_ordersdf['shop_id'] = $this->__channelObj->channel['shop_id'];
        $this->_ordersdf['shop_type'] = $this->__channelObj->channel['shop_type'];
        $this->_ordersdf['org_id'] = $this->__channelObj->channel['org_id'];

        if (isset($this->_ordersdf['order_objects']) && !empty($this->_ordersdf['order_objects'])) {
            // 矩阵返回优惠数据不请求接口，否则请求接口
            $ext_data = array();
            $ext_data['shop_id'] = $this->__channelObj->channel['shop_id'];
            $ext_data['shop_type'] = $this->__channelObj->channel['shop_type'];
            $ext_data['createtime'] = $this->_ordersdf['createtime'];
            $ext_data['order_bn'] = $this->_ordersdf['order_bn'];
            $ext_data['coupon_source'] = 'push';
            // 优惠明细数据format
            $result = kernel::single('ome_order_coupon')->couponDataFormat($this->_ordersdf['order_objects'], $ext_data, $ext_data['shop_type']);

            $this->_ordersdf['coupon_data'] = $result['coupon_data'];
            $this->_ordersdf['objects_coupon_data'] = $result['objects_coupon_data'];
        }
    }

    protected function get_create_plugins()
    {
        $plugins = parent::get_create_plugins();

//        $plugins[] = 'coupon';
        $plugins[] = 'orderextend';
        $plugins[] = 'couponecshopx';


        return $plugins;
    }

    public function get_update_plugins()
    {
        $plugins = parent::get_update_plugins();

        // 未支付变已支付 支付单缺失问题
        if ($this->_tgOrder['pay_status'] == '0' && $this->_ordersdf['pay_status'] == '1' && !in_array('payment', $plugins)) {
            $plugins[] = 'payment';
        }
        $plugins[] = 'couponecshopx';

        return $plugins;
    }
}
