<?php
class erpapi_shop_matrix_shopex_ecshopx_response_refund extends erpapi_shop_matrix_shopex_response_refund {

    //全民分销未生成退款单前允许编辑
    public function _formatAddParams($params) {
        $sdf=parent::_formatAddParams($params);
        $new_memo = is_string($sdf['memo']) ? json_decode($sdf['memo'], true) : $sdf['memo'];
        if(is_array($new_memo) && $new_memo['refund_items']){
            $sdf['memo'] = $new_memo['memo'] ?: '';
            $sdf['bn'] = $new_memo['refund_items'][0]['bn'] ?: '';
            $sdf['oid'] = $new_memo['refund_items'][0]['oid'] ?: '';
            $sdf['product_data'] = serialize($new_memo['refund_items'][0]);
        }
        $sdf['t_received'] = time();
        // 接收0元退款单
        $sdf['is_zero_accept'] = true;
        if($sdf['status']!='4') {
            $sdf['refund_version_change'] = true;
        }

        # 金额不一致处理
        if(bccomp($sdf['cur_money'], $sdf['money'],2) > 0 && floatval($sdf['money']) > 0){
            $sdf['money'] = $sdf['cur_money'];
        }
        return $sdf;
    }

    public function add($params) {
        $sdf=parent::add($params);
        if ($sdf['order']['ship_status']) {
            $sdf['refund_refer']=in_array($sdf['order']['ship_status'],array('1','3')) ? '1' : '0';
        }
        return $sdf;
    }
}