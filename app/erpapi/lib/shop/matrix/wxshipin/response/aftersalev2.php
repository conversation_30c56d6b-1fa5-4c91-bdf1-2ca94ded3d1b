<?php

/**
 * @Author: <EMAIL>
 * @Date: 2023/4/20
 * @Describe: 售后接收类
 */
class erpapi_shop_matrix_wxshipin_response_aftersalev2 extends erpapi_shop_response_aftersalev2
{
    protected function _getAddType($sdf)
    {
        //需要退货才更新为售后单
        if ($sdf['has_good_return'] == 'true') {
            if (in_array($sdf['order']['ship_status'], array('0'))) {
                //有退货，未发货的,做退款
                return 'refund';
            } else {
                //识别如果是已完成的售后，转成退款单更新的逻辑
                if (strtolower($sdf['status']) == 'success') {
                    $refundOriginalObj = app::get('ome')->model('return_product');
                    //退货状态必须是已完成
                    $refundOriginalInfo = $refundOriginalObj->getList('return_id', array('return_bn' => $sdf['refund_bn'], 'status' => '4'), 0, 1);

                    if ($refundOriginalInfo) {
                        $refundApplyObj = app::get('ome')->model('refund_apply');
                        //售后退款申请单的退款状态，不能是已退款
                        $refundApplyInfo = $refundApplyObj->getList('refund_apply_bn', array('return_id' => $refundOriginalInfo[0]['return_id'], 'status' => array('0', '1', '2', '5', '6')), 0, 1);
                        if ($refundApplyInfo) {
                            $sdf['refund_bn'] = $refundApplyInfo[0]['refund_apply_bn'];
                            return 'refund';
                        }
                    }
                }

                //有退货，已发货的,做售后
                return 'returnProduct';
            }
        } else {
            //无退货的，直接退款
            return 'refund';
        }
    }

    protected function _formatAddItemList($sdf, $convert = array())
    {
        $convert = array(
            'sdf_field' => 'oid',
            'order_field' => 'oid',
            'default_field' => 'outer_id'
        );

        return parent::_formatAddItemList($sdf, $convert);
    }

    protected function _refundAddSdf($sdf)
    {
        $refundOriginalObj = app::get('ome')->model('return_product');
        $refundApplyObj = app::get('ome')->model('refund_apply');

        if (strtolower($sdf['status']) == 'success') {
            //退货状态必须是已完成
            $refundOriginalInfo = $refundOriginalObj->getList('return_id', array('return_bn' => $sdf['refund_bn'], 'status' => '4'), 0, 1);
            if ($refundOriginalInfo) {
                //售后退款申请单的退款状态，不能是已退款
                $refundApplyInfo = $refundApplyObj->getList('refund_apply_bn', array('return_id' => $refundOriginalInfo[0]['return_id'], 'status' => array('0', '1', '2', '5', '6')), 0, 1);
                if ($refundApplyInfo) {
                    $sdf['refund_bn'] = $refundApplyInfo[0]['refund_apply_bn'];
                }
            }
        }

        $sdf = parent::_refundAddSdf($sdf);

        return $sdf;
    }

    protected function _formatAddParams($params)
    {
        $sdf = parent::_formatAddParams($params);

        //平台售后单状态
        if ($params['status']) {
            $sdf['platform_status'] = trim($params['status']);
            $sdf['source_status'] = trim($params['status']);
        }

        return $sdf;
    }
    /**
     * 售后业务
     * @param $params
     * @return array|false
     */
    public function add($params)
    {
        $this->__apilog['title'] = '店铺(' . $this->__channelObj->channel['name'] . ')售后业务处理[订单：' . $params['tid'] . ']';
        $this->__apilog['original_bn'] = $params['tid'];
        $this->__apilog['result']['data'] = array('tid' => $params['tid'], 'aftersale_id' => $params['refund_id'], 'retry' => 'false');
        $sdf = $this->_formatAddParams($params);
        if (empty($sdf) || !is_array($sdf)) {
            if (!$this->__apilog['result']['msg']) {
                $this->__apilog['result']['msg'] = '没有数据,不接收售后单';
            }
            return false;
        }
        $shopId = $sdf['shop_id'] = $this->__channelObj->channel['shop_id'];
        $sdf['shop_type'] = $this->__channelObj->channel['shop_type'];
        $sdf['shop']['delivery_mode'] = $this->__channelObj->channel['delivery_mode'];
        $field = 'order_id,status,process_status,ship_status,pay_status,payed,cost_payment,pay_bn,member_id,logi_id,logi_no,ship_name,ship_area,ship_addr,ship_zip,ship_tel,ship_email,ship_mobile,shipping,is_protect,is_cod,source,order_type,createtime,abnormal,source_status,shop_type';
        $tgOrder = $this->getOrder($field, $shopId, $sdf['order_bn']);

        //删除退款日志
        if (!$tgOrder && in_array(strtoupper($sdf['status']), array('SELLER_REFUSE_BUYER', 'SUCCESS', 'CLOSED'))) {
            $filter = array(
                'order_bn' => $sdf['order_bn'],
                'shop_id' => $sdf['shop_id'],
                'refund_bn' => $sdf['refund_bn']
            );
            app::get('ome')->model('refund_no_order')->delete($filter);
            $tgOrder = $this->getOrder($field, $shopId, $sdf['order_bn']);
        }

        //添加退款日志
        if (!$tgOrder) {
            if (!in_array(strtoupper($sdf['status']), array('SELLER_REFUSE_BUYER', 'SUCCESS', 'CLOSED'))) {
                $this->_dealRefundNoOrder($sdf);
            }
            $this->__apilog['result']['msg'] = '没有订单' . $sdf['order_bn'];
            return false;
        }

        $sdf['order'] = $tgOrder;

        //[换货完成又退货]获取换货产生的OMS订单进行退货操作
        list($is_change, $change_msg, $change_order, $convert) = $this->getChangeReturnProduct($sdf);

        if ($is_change === true) {
            // OMS生成的新订单号
            $sdf['tid'] = $sdf['order_bn'] = $change_order['order_bn'];
            // OMS换货生成的新订单信息
            $sdf['order'] = $change_order;
        }

        $type = $this->_getAddType($sdf);
        if (empty($type)) {
            if (!$this->__apilog['result']['msg']) {
                $this->__apilog['result']['msg'] = '所属店铺类型,不接收售后单';
            }
            return false;
        }

        //未签收的售后仅退款转为售后退货
        if ($type == 'refund'
            && $sdf['order']['ship_status'] == '1'
            && $sdf['order']['source_status'] != 'TRADE_FINISHED'
            && app::get('ome')->getConf('ome.reship.refund.only.reship') == 'true'
        ) {
            $sdf['refund_to_returnProduct'] = true;
            $type = 'returnProduct';
        }

        //微信视频号售后申请单类型为refund存在已发货发货单,且状态为待审核和关闭,标记为退款类型的售后申请单
        if ($type == 'refund' && $this->isSentOfOrderObject($sdf) && in_array($sdf['status'], ['WAIT_SELLER_AGREE', 'SELLER_REFUSE_BUYER', 'SUCCESS', 'CLOSED'])) {

            //如果是退款完成的，还要继续判断是否已经存在退款申请单，如果存在，则不能转售后申请
            $refundApplyObj = app::get('ome')->model('refund_apply');
            $refundApplyInfo = $refundApplyObj->getList('apply_id', array('refund_apply_bn' => $sdf['refund_bn'], 'status' => array('0', '1', '2', '5', '6')), 0, 1);
            if (isset($refundApplyInfo[0]['apply_id']) && $refundApplyInfo[0]['apply_id'] && $sdf['status'] == 'SUCCESS') {
                //保持退款类型，更新退款状态，不转售后申请

            } else {
                $sdf['refund_to_returnProduct'] = true;
                $type = 'returnProduct';
            }
        }

        //识别如果是已完成的售后，转成退款单更新的逻辑
        if (in_array($type, ['returnProduct', 'reship']) && strtolower($sdf['status']) == 'success') {
            $refundOriginalObj = app::get('ome')->model('return_product');
            $refundOriginalInfo = $refundOriginalObj->getList('return_id', array('return_bn' => $sdf['refund_bn'], 'status' => '4'), 0, 1);
            if ($refundOriginalInfo) {
                $refundApplyObj = app::get('ome')->model('refund_apply');
                $refundApplyInfo = $refundApplyObj->getList('refund_apply_bn', array('return_id' => $refundOriginalInfo[0]['return_id'], 'status' => array('0', '1', '2', '5', '6')), 0, 1);
                if ($refundApplyInfo) {
                    $sdf['refund_bn'] = $refundApplyInfo[0]['refund_apply_bn'];
                    $sdf['tmall_has_finished_return_product'] = true;
                    $type = 'refund';
                }
            }
        }
        if (is_array($sdf['refund_item_list'])) {
            if (!$sdf['change_order_id']) {
                $refundItemList = $this->_formatAddItemList($sdf);
                if (empty($refundItemList)) {
                    $sdf['refund_item_list'] = '';
                } else {
                    $sdf['refund_item_list'] = $this->_calculateAddPrice($refundItemList, $sdf);
                }
            }

        }

        if ($type == 'refund') {
            return $this->_refundAddSdf($sdf);
        } elseif ($type == 'returnProduct') {
            return $this->_returnProductAddSdf($sdf);
        } elseif ($type == 'reship') {
            return $this->_reshipAddSdf($sdf, $params);
        } else {
            if (!$this->__apilog['result']['msg']) {
                $this->__apilog['result']['msg'] = '不接收售后单';
            }
            return false;
        }
    }

    /**
     * 商品格式增加outer_id字段
     * @param $sdf
     * @return array[]
     */
    public function _formatAddField($sdf)
    {
        $convert = array(
            'sdf_field' => 'oid',
            'order_field' => 'oid',
            'default_field' => 'outer_id'
        );
        $sdfField = $convert['sdf_field'];
        $orderField = $convert['order_field'];
        $defaultField = $convert['default_field'];

        $itemList = is_array($sdf['refund_item_list']) ? $sdf['refund_item_list'] : json_decode($sdf['refund_item_list'], true);
        $itemList = $itemList['return_item'];
        $arrOrderField = array();
        $arrBn = array();
        foreach ($itemList as $val) {
            if ($val[$sdfField]) {
                $arrOrderField[] = $val[$sdfField];
            }
        }

        $filter = array(
            $orderField => $arrOrderField,
            'order_id' => $sdf['order']['order_id']
        );
        $object = app::get('ome')->model('order_objects')->getList($orderField . ',bn', $filter);
        foreach ($object as $oVal) {
            $arrBn[$oVal[$orderField]] = $oVal['bn'];
        }

        $arrItem = array();
        foreach ($itemList as $item) {
            $item[$defaultField] = $arrBn[(string)$item[$sdfField]] ? $arrBn[(string)$item[$sdfField]] : $item[$defaultField];
            $arrItem[] = $item;
        }
        $result = [
            'return_item' => $arrItem
        ];
        return $result;
    }
}