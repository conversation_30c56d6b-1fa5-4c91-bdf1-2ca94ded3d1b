<?php

class erpapi_kucun100_response_process_stock
{
    protected $storeList = [];

    public function notify($sdf)
    {
        if (empty($sdf['stock'])) {
            return array('rsp' => 'fail', 'msg' => '库存信息不能为空');
        }

        $materialObj = kernel::single('material_kucun100');
        $storeMdl = app::get('o2o')->model('store');
        $basicMdl = app::get('material')->model('basic_material');
        $branchMdl = app::get('ome')->model('branch');
        $oBranchPro = app::get('ome')->model('branch_product');
        $stockLogMdl = app::get('material')->model('stock_sync_log');
        $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');

        $warnList = [];
        # 读取库存预警设置
        $warningOpen = app::get('ome')->getConf('ome.store.warning.open');
        # 获取商品列表
        foreach ($sdf['stock'] as $param) {
            # 获取门店信息
            if (empty($this->storeList[$param['store_bn']])) {
                $storeInfo = $storeMdl->dump(array('store_bn' => $param['store_bn']), 'store_id,store_bn');
                if (!empty($storeInfo)) {
                    $this->storeList[$param['store_bn']] = $storeInfo;
                }
            }
            $storeInfo = $this->storeList[$param['store_bn']];
            # 门店信息不存在无需处理
            if (empty($storeInfo)) {
                continue;
            }

            # 检查sku是否存在
            $basicInfo = $basicMdl->dump(array('material_bn' => $param['material_bn']), 'bm_id,material_bn,material_name');
            if (empty($basicInfo)) {
                continue;
            }

            # 获取仓库
            if (empty($this->storeList[$param['store_bn']]['branch_id'])) {
                $branchInfo = $branchMdl->dump(array('store_id' => $storeInfo['store_id']), 'branch_id');
                if (!empty($branchInfo)) {
                    $this->storeList[$param['store_bn']]['branch_id'] = $branchInfo['branch_id'];
                }
            }
            $branch_id = $this->storeList[$param['store_bn']]['branch_id'];
            # 没有门店对应的仓库无需处理
            if (empty($branch_id)) {
                continue;
            }

            # 查询库存
            $branchPro_info = $oBranchPro->dump(array('branch_id' => $branch_id, 'product_id' => $basicInfo['bm_id']), 'store,unit_cost');
            if (empty($branchPro_info)) {
                $branchPro_info['store'] = 0;
                $branchPro_info['unit_cost'] = 0;
            }

            # 接收库存 100 的库存计算调整为可用库存+冻结库存
            $param['store'] = bcadd($param['store'], $param['lock_store'], 0);
            # 检查库存数
            $store = intval($param['store']) < 0 ? 0 : $param['store'];
            # 订单冻结库存
            $store_freeze = $basicMStockFreezeLib->getShopFreezeByBmid($basicInfo['bm_id']);
            # 仓库冻结库存
            $branch_freeze = $basicMStockFreezeLib->getBranchFreezeByBmid($basicInfo['bm_id']);

            # 记录库存日志
            if (!empty($warningOpen) && $warningOpen == 'true') {
                $log_data = [
                    'store_bn' => $param['store_bn'],
                    'bm_id' => $basicInfo['bm_id'],
                    'num' => $store,
                    'order_freeze' => $store_freeze,
                    'branch_freeze' => $branch_freeze,
                    'warning_flag' => 'wait',
                    'warning_status' => 'wait',
                    'source' => '全量库存同步',
                    'createtime' => time()
                ];
                $stockLogMdl->save($log_data);
            }

            # 库存100推送库存时，如果同步过来的库存小于OMS订单预占+仓库冻结，就需要预警 <EMAIL>
            if (bccomp($store_freeze + $branch_freeze, $store) > 0) {
                $tmp_data = [
                    'store_bn' => $param['store_bn'],
                    'bm_id' => $basicInfo['bm_id'],
                    'bn' => $param['material_bn'],
                    'material_name' => $basicInfo['material_name'],
                    'num' => $store,
                    'order_freeze' => $store_freeze,
                    'branch_freeze' => $branch_freeze,
                    'createtime' => time()
                ];
                $warnList[] = $tmp_data;
            }

            # 判断原数量和修改的数量是否相同
            if (($diff_nums = $branchPro_info['store'] - $store) == 0) {
                continue;
            }

            $type = $diff_nums < 0 ? "IN" : "OUT";
            if ($type == 'IN') {
                $adjustLib = kernel::single('siso_receipt_iostock_adjustin');
                $adjustLib->_typeId = 70;
            } else {
                $adjustLib = kernel::single('siso_receipt_iostock_adjustout');
                $adjustLib->_typeId = 7;
            }

            # 增加出入库明细
            $data = array();
            $data['product_id'] = $basicInfo['bm_id'];
            $data['branch_id'] = $branch_id;
            $data['to_nums'] = $store;

            $iostockData = array();
            $iostockData['branch_id'] = $data['branch_id'];
            $iostockData['operator'] = 'system';
            $iostockData['bill_type'] = 'branchadjust';
            $iostockData['items'][] = array(
                'bn' => $basicInfo['material_bn'],
                'iostock_price' => $branchPro_info['unit_cost'],
                'oper' => 'system',
                'nums' => abs($diff_nums),
                'memo' => '库存100-全量更新',
            );
            $error_msg = '';
            $adjustLib->create($iostockData, $data, $error_msg);

            # 更新仓库库存
            $materialObj->updateBranchProduct($store, $basicInfo['bm_id'], $branch_id, $storeInfo);
            # 更新基础物料库存
            $materialObj->updateProduct($store, $basicInfo['bm_id']);
        }

        # 库存100推送库存时，如果同步过来的库存小于OMS订单预占+仓库冻结，就需要预警 <EMAIL>
        if (!empty($warnList)) {
            $queueParams = array(
                'data' => array(
                    'params' => serialize($warnList),
                    'bill_type' => 'stock_warn',
                    'log_id' => time(),
                    'task_type' => 'sendemail',
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service'),
            );
            kernel::single('taskmgr_interface_connecter')->push($queueParams);
        }
        return array('rsp' => 'succ');
    }


}