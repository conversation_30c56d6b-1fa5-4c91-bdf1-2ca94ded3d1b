<?php

/**
 * 商品异动通知
 */
class erpapi_kucun100_request_goods extends erpapi_kucun100_request_abstract
{

    protected $__original_bn;

    public function getAllList($sdf)
    {
        $title = '库存100-获取商品列表（批量方式，页码：' . $sdf['page'] . '）';
        $this->__original_bn = 'get_all_material';

        $requestParams = array(
            'pageIndex' => empty($sdf['page']) ? 1 : intval($sdf['page']),
            'pageSize' => empty($sdf['limit']) ? 100 : intval($sdf['limit']),
        );
        if (!empty($sdf['store_bn'])) {
            $requestParams['customerStoreCode'] = $sdf['store_bn'];
        }
        if (!empty($sdf['spu_id'])) {
            $requestParams['spuCodes'] = $sdf['spu_id'];
        }
        $params['bizContent'] = $requestParams;
        # 增加数据来源参数，用于判断域名
        $params['source'] = $sdf['source'];

        # 请求接口
        $callback = array();
        $count = 0;
        do {
            $response = $this->__caller->call(KUCUN100_GETSPULIST, $params, $callback, $title, 10, $this->__original_bn);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['msg'])) {
                break;
            }

            $count++;
        } while ($count < 3);

        # 重新设置返回参数
        if ($response['rsp'] == 'succ' && !empty($response['data']['list'])) {
            $response['totalPage'] = $response['data']['totalPage'];
            $response['totalSize'] = $response['data']['totalSize'];
            $response['data'] = $this->_format_callback_goods_params($response['data']['list']);
        }
        return $response;
    }

    /**
     * 获取商品列表
     * @param $sdf
     * @return array|mixed
     */
    public function getSpuList($sdf)
    {
        if (empty($sdf['store_bn'])) {
            return $this->error('门店编码不能为空！');
        }
        if (empty($sdf['spu_id'])) {
            return $this->error('商品编码(唯一码)不能为空');
        }

        $title = '库存100-获取商品列表';
        $spu_id = is_array($sdf['spu_id']) ? implode(',', $sdf['spu_id']) : $sdf['spu_id'];
        $this->__original_bn = current(explode(',', $spu_id));

        //params
        $requestParams = array(
            'customerStoreCode' => $sdf['store_bn'],
            'spuCodes' => $spu_id,
            'pageIndex' => 1,
            'pageSize' => 500
        );
        $params['bizContent'] = $requestParams;

        # 请求接口
        $callback = array();
        $count = 0;
        do {
            # 增加数据来源参数，用于判断域名
            $params['source'] = $sdf['source'];
            $response = $this->__caller->call(KUCUN100_GETSPULIST, $params, $callback, $title, 10, $this->__original_bn);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['msg'])) {
                break;
            }

            $count++;
        } while ($count < 3);

        # 失败直接返回
        if ($response['rsp'] == 'fail') {
            return $response;
        }

        # sku列表
        $materialList = $response['data']['list'];
        if (empty($materialList)) {
            $response['rsp'] = 'fail';
            $response['msg'] = '读取到的sku列表信息为空';
            return $response;
        }

        # 重新设置返回参数
        $material_list = $this->_format_callback_goods_params($materialList);
        if (empty($material_list)) {
            $response['rsp'] = 'fail';
            $response['msg'] = 'sku列表为空或不存在大码信息';
            return $response;
        }
        $response['data'] = $material_list;
        return $response;
    }

    /**
     * 获取商品库存列表
     * @param $sdf
     * @return void
     */
    public function getInventoryList($sdf)
    {
        if (empty($sdf['store_bn'])) {
            return $this->error('门店编码不能为空！');
        }
        if (empty($sdf['sku_id'])) {
            return $this->error('基础物料编码不能为空');
        }

        $title = '库存100-获取商品库存列表';
        $spu_id = is_array($sdf['spu_id']) ? implode(',', $sdf['spu_id']) : $sdf['spu_id'];
        $this->__original_bn = current(explode(',', $spu_id));

        //params
        $requestParams = array(
            'customerStoreCodes' => $sdf['store_bn'],
            'skuCodes' => is_array($sdf['sku_id']) ? implode(',', $sdf['sku_id']) : $sdf['sku_id'],
            'pageIndex' => 1,
            'pageSize' => 500
        );
        if (empty($requestParams['skuCodes'])) {
            return $this->error('基础物料编码不能为空');
        }
        $params['bizContent'] = $requestParams;
        # 增加数据来源参数，用于判断域名
        $params['source'] = $sdf['source'];

        # 请求接口
        $callback = array();
        $count = 0;
        do {
            $response = $this->__caller->call(KUCUN100_GETINVENTORYLIST, $params, $callback, $title, 10, $this->__original_bn);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['msg'])) {
                break;
            }
            $count++;
        } while ($count < 3);

        # 失败直接返回
        if ($response['rsp'] == 'fail') {
            return $response;
        }

        # sku库存列表
        $stockList = $response['data']['list'];
        if (empty($stockList)) {
            $response['rsp'] = 'fail';
            $response['msg'] = '读取到的sku库存信息为空';
            return $response;
        }

        # 重新设置返回参数
        $material_list = $this->_format_callback_store_params($stockList);
        if (empty($material_list)) {
            $response['rsp'] = 'fail';
            $response['msg'] = 'sku列表为空或不存在大码信息';
            return $response;
        }
        $response['data'] = $material_list;
        return $response;
    }

    /**
     * 格式化返回的sku参数
     * @param $materialList
     * @return array|void
     */
    private function _format_callback_goods_params($materialList)
    {
        if (empty($materialList)) {
            return [];
        }

        $result = [];
        foreach ($materialList as $item) {
            # 没有大码的商品不创建基础物料
            if (empty($item['skuList']) || empty($item['largeSize'])) {
                continue;
            }

            $master = [
                'spu_id' => $item['spuCode'],
                'material_name' => $item['spuName'],
                'store_bn' => $item['customerStoreCode'],
                'status' => $item['status'],  // 商品状态：0-下架，1-上架
                'category1_bn' => $item['outBigCategoryCode'], // 1级分类
                'category1_name' => $item['outBigCategoryName'],
                'category2_bn' => $item['outMidCategoryCode'], // 2级分类
                'category2_name' => $item['outMidCategoryName'],
                'category3_bn' => $item['outCategoryCode'], // 3级分类
                'category3_name' => $item['outCategoryName'],
                'brand_bn' => $item['branchCode'], // 品牌
                'brand_name' => $item['branchName'],
                'material_spu_id' => $item['largeSize'],  // 商品大码
                'supply_model' => $item['supplyModel'], // 发货模式
                'busness_material_bn' => $item['itemNo'], // 商户货号
                'updatetime' => empty($item['updateTime']) ? 0 : strtotime($item['updateTime']),
            ];

            foreach ($item['skuList'] as $sku) {
                $data = [
                    'material_bn' => $sku['skuCode'],
                    'material_spu' => $sku['erpSkuCode'],
                    'barcode' => $sku['barCode'],
                    'cost' => $sku['markingPrice'],
                    'retail_price' => $sku['retailPrice'],
                    'brand_sku_code' => $sku['brandSkuCode'], // 品牌SKU编码
                ];
                foreach ($sku['skuAttributeList'] as $attr) {
                    if ($attr['name'] == '颜色') {
                        $data['color'] = $attr['val'];
                    }
                    if ($attr['name'] == '尺码') {
                        $data['size'] = $attr['val'];
                    }
                }
                $result[] = array_merge($master, $data);
            }
        }
        return $result;
    }

    /**
     * 格式化返回的库存参数
     * @param $stockList
     * @return array
     */
    private function _format_callback_store_params($stockList)
    {
        if (empty($stockList)) {
            return [];
        }

        $result = [];
        foreach ($stockList as $item) {
            $data = [
                'store_bn' => $item['customerStoreCode'],
                'spu_id' => $item['spuCode'],
                'material_bn' => $item['skuCode'],
                'store' => $item['inventoryCount'],     // 可用库存
                'lock_store' => $item['lockInventoryCount'] ?? 0,  // 锁定库存
                'updatetime' => empty($item['updateTime']) ? time() : strtotime($item['updateTime'])
            ];
            $result[] = $data;
        }
        return $result;
    }
}
