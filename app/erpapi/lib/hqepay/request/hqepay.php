<?php
/**
 * 华强宝处理
 *
 * @category
 * @package
 * <AUTHOR>
 * @version $Id: Z
 */
class erpapi_hqepay_request_hqepay extends erpapi_hqepay_request_abstract
{
    const _TO_NODE_ID = '1995170839';

    #订阅华强宝(订单分发的信息(具体订单到分给了的网点，业务员的信息))
    public function pub($sdf, $queue = false)
    {

        $args = func_get_args();
        array_pop($args);
        $_in_mq = $this->__caller->caller_into_mq('hqepay_pub', 'hqepay', $this->__channelObj->channel['hqepay_id'], $args, $queue);
        if ($_in_mq) {
            return $this->succ('成功放入队列');
        }

        $params               = $sdf;
        //$params['node_type']  = 'hqepay';
        //$params['to_node_id'] = self::_TO_NODE_ID;
        $title                = sprintf('添加物流订阅[%s]', $sdf['logistic_code']);

        if ($this->check_customer_name($params) && !is_numeric($params['customer_name'])) {

            if(isset($params['delivery_logistic_code']) && $params['delivery_logistic_code']){
                $deliveryBill = app::get('wap')->model('delivery_bill')->dump(array('logi_no' => $params['delivery_logistic_code']), 'consigner_mobile');
            }else{
                $deliveryBill = app::get('wap')->model('delivery_bill')->dump(array('logi_no' => $params['logistic_code']), 'consigner_mobile');
            }

            $params['customer_name'] = substr($deliveryBill['consigner_mobile'], -4);
        }

        if ($this->check_customer_name($params) && empty($params['customer_name'])) {
            if(isset($params['delivery_logistic_code']) && $params['delivery_logistic_code']){
                $deliveryShipping = app::get('ome')->model('delivery_shipping')->db_dump(array('logi_no' => $params['delivery_logistic_code']), 'logistics_delivery_mobile');
                $params['customer_name'] = substr($deliveryShipping['logistics_delivery_mobile'], -4) ?? '';
            }else{
                $deliveryShipping = app::get('ome')->model('delivery_shipping')->db_dump(array('logi_no' => $params['logistic_code']), 'logistics_delivery_mobile');
                $params['customer_name'] = substr($deliveryShipping['logistics_delivery_mobile'], -4) ?? '';
            }
        }

        $callback = array(
            'class'  => get_class($this),
            'method' => 'pubcallback',
            'params' => array(
                'obj_bn' => $sdf['delivery_bn'],
                'logi_no'=> $sdf['logistic_code']
            ),
        );

        return $this->__caller->call(SHOP_LOGISTICS_SUBSCRIBE, $params, $callback, $title, 10, $sdf['delivery_bn']);
    }

    /**
     * 检测是否要取 customer_name
     * @param $params
     */
    private function check_customer_name($params)
    {
        if (in_array(strtoupper($params['company_code']), ['SF', 'ZTO'])) {
            return true;
        }

        if(in_array($params['shop_type'],[XCXSHOPTYPE])){
            return true;
        }

        return false;
    }

    /**
     * 回调
     * @param $response Array
     * @param $callback_params Array
     * @return array
     **/
    public function pubcallback($response, $callback_params)
    {
        $logi_no = $callback_params['logi_no'];
        if($response['rsp'] == 'fail' && ome_func::contains($response['err_msg'], '手机尾号不正确')) {
            //如果是补录运单,修改为已揽收
            $waybill_info = app::get('logisticsmanager')->model('waybill')->dump(['waybill_number' => $logi_no]);
            if(!$waybill_info){
                $db = kernel::database();
                $ome_delivery_obj = app::get('ome')->model('delivery');
                $log_obj = app::get('ome')->model('operation_log');
                $delivery_list = $ome_delivery_obj->getList('delivery_bn,delivery_id,logi_status,shop_type',['logi_no'=>$logi_no]);
                foreach ($delivery_list as $delivery_info){
                    if($delivery_info['logi_status'] != '7'){
                        continue;
                    }
                    $delivery_id = $delivery_info['delivery_id'];
                    $delivery_bn = $delivery_info['delivery_bn'];

                    $db->query("update sdb_ome_delivery set logi_status = '1' where delivery_id = $delivery_id");
                    $db->query("update sdb_wap_delivery set logi_status = '1' where outer_delivery_bn = '$delivery_bn' ");

                    $log_obj->write_log('delivery_process@ome',$delivery_id,'补录订单号订阅提示手机尾号不正确,改成已揽收');

                }
            }
        }

        return $response;
    }

    /**
     * 物流查询
     *
     * @return void
     * <AUTHOR>
    public function query($sdf)
    {
        $params = array(
            //'to_node_id'    => self::_TO_NODE_ID,
            'tid'           => $sdf['order_bn'],
            'company_code'  => $sdf['logi_code'],
            'company_name'  => $sdf['company_name'],
            'logistic_code' => $sdf['logi_no'],
        );
        if ($sdf['customer_name']) $params['customer_name'] = $sdf['customer_name'];

        $title = sprintf('查询物流信息[%s]', $params['logistic_code']);

        return $this->__caller->call(LOGISTICS_TRACE_DETAIL_GET, $params, array(), $title, 5, $params['logistic_code']);
    }

    /**
     * 华强宝绑定
     *
     * @return void
     * <AUTHOR>
    public function bind()
    {
        $params = array(
            'app'           => 'app.applyNodeBind',
            'node_id'       => base_shopnode::node_id('ome'),
            'from_certi_id' => base_certificate::certi_id(),
            'callback'      => '',
            'sess_callback' => '',
            'api_url'       => kernel::base_url(1) . kernel::url_prefix() . '/api',
            'node_type'     => 'hqepay',
            'to_node'       => self::_TO_NODE_ID,
            'shop_name'     => '物流跟踪',
            "api_key"       => "1236217",
            "api_secret"    => "cf98e49d-9ebe-43cb-a690-ad96295b3457",
        );

        $params['certi_ac'] = $this->_gen_bind_sign($params);

        $title = '华强宝订阅';

        $callback = array();

        $result = $this->__caller->call(SHOP_LOGISTICS_BIND, $params, $callback, $title, 4);

        $response = json_decode($result['response'], true);

        if ($response['res'] == 'succ' || $response['msg']['errorDescription'] == '绑定关系已存在,不需要重复绑定') {
            return true;
        } else {
            return false;
        }
    }

    private function _gen_bind_sign($params)
    {
        $token = base_certificate::token();

        ksort($params);
        $str = '';
        foreach ($params as $key => $value) {
            $str .= $value;
        }

        $sign = md5($str . $token);

        return $sign;
    }

}
