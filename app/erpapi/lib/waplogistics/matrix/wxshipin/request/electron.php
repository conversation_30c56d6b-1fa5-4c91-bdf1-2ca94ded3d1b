<?php

class erpapi_waplogistics_matrix_wxshipin_request_electron extends erpapi_waplogistics_request_electron
{
    /**
     * <AUTHOR> 2023-10-09 16:38
     * @describe 电子面单预取号
     */
    public function directRequestPre($sdf = '', &$params)
    {
        $this->title = '微信视频号-电子面单预取号';
        $this->timeOut = 20;
        $this->primaryBn = $sdf['primary_bn'];

        $sender = [
            'name' => $sdf['shop']['default_sender'], // 人名
            'mobile' => $sdf['shop']['mobile'] ?: $sdf['shop']['tel'], // 联系电话
            'province' => $sdf['shop']['province'], // 省
            'city' => $sdf['shop']['city'], // 市
            'county' => $sdf['shop']['area'], // 区
            'street' => $sdf['shop']['street'], // 街道
            'address' => $sdf['shop']['address_detail'], // 详细地址
        ];
        $receiver = [
            'name' => $sdf['delivery']['consignee']['name'], // 人名
            'mobile' => $sdf['delivery']['consignee']['mobile'], // 联系电话
            'province' => $sdf['delivery']['consignee']['province'], // 省
            'city' => $sdf['delivery']['consignee']['city'], // 市
            'county' => $sdf['delivery']['consignee']['district'], // 区
            'street' => $sdf['delivery']['consignee']['town'], // 街道
            'address' => $sdf['delivery']['consignee']['addr'], // 详细地址
        ];
        $goods_list = [];
        foreach ($sdf['delivery_item'] as $k => $v) {
            $goods_info = [
                'good_name' => $this->charFilter($v['product_name']), // 商品名
                'good_count' => (int)$v['number'], // 商品个数
                'product_id' => (int)$v['shop_goods_id'], // 商品product id
                'sku_id' => (int)$v['shop_product_id'], // 商品sku id
            ];
            if (!$v['shop_goods_id'] || $v['shop_goods_id'] == '-1') {
                //本地商品或本地赠品不传product_id和sku_id
                unset($goods_info['product_id'], $goods_info['sku_id']);
            }
            $goods_list[] = $goods_info;
        }
        $ec_order_list = [[
            'ec_order_id' => (int)(empty($sdf['order'][0]['source_order_bn']) ? $sdf['order'][0]['order_bn'] : $sdf['order'][0]['source_order_bn']),
            'goods_list' => $goods_list,
        ]];

        // 代发模式
        if ($sdf['delivery']['shop']['addon']['unikey'] && $sdf['delivery']['shop']['shop_id'] != $this->__channelObj->channel['shop_id']) {
            $original = app::get('ome')->model('order_receiver')->db_dump(['order_id' => $sdf['order'][0]['order_id']], 'encrypt_source_data');
            if ($original) {
                $encrypt_source_data = json_decode($original['encrypt_source_data'], 1);
                if ($encrypt_source_data['ewaybill_order_code']) {
                    $ec_order_list = [[
                        'goods_list' => $goods_list,
                        'ewaybill_order_code' => $encrypt_source_data['ewaybill_order_code'],
                        'ewaybill_order_appid' => $sdf['delivery']['shop']['addon']['unikey'],
                    ]];
                }
            }
        }

        $remark = [];
        foreach ($sdf['order'] as $ok => $ov) {
            if ($ov['custom_mark']) {
                $tmp = unserialize($ov['custom_mark']);
                $remark[] = str_replace(["\t", "\r\n", "\r", "\n", "'", "\"", "\\"], '', $tmp['op_content']);
            }
            if ($ov['mark_text']) {
                $tmp = unserialize($ov['mark_text']);
                $remark[] = str_replace(["\t", "\r\n", "\r", "\n", "'", "\"", "\\"], '', $tmp['op_content']);
            }
        }

        $params = [
            'order_bn' => json_encode(array_column($sdf['order'], 'source_order_bn')), // oms日志记录的时候存int类型有问题,所以单独存一个字段
            'delivery_id' => $sdf['dly_corp']['type'], // 快递公司id
            'site_code' => $sdf['shop']['site_code'] ?? '', // 网点编码
            'ewaybill_acct_id' => $sdf['shop']['acct_id'] ?? $sdf['customer_code'], // 客户编码
            'sender' => json_encode($sender), // 寄件人,传明文
            'receiver' => json_encode($receiver), // 收件人,传小店订单内获取到的用户信息即可
            'ec_order_list' => json_encode($ec_order_list), // 订单信息
            'remark' => $remark ? implode('; ', $remark) : '', // 备注
            'shop_id' => $sdf['shop']['shop_id'] ?? $sdf['extend']['shop_id'], // 店铺id（从查询开通账号信息接口获取）
            'template_type' => 'single',  // 如无需使用后台模板，可直接传递	template_type做为默认模板， 如‘single’
        ];

        $jst = [];
        $gateway = '';
        $result = $this->requestCall(STORE_WAYBILL_PRE_GET, $params, array(), $jst, $gateway);
        $waybill = empty($result['data']) ? array() : json_decode($result['data'], true);

        if ($result['rsp'] == 'succ' && $waybill['ewaybill_order_id']) {
            // 取号成功以后，会保存在sdb_logisticsmanager_waybill_extend的print_config里
            $params['ewaybill_order_id'] = $waybill['ewaybill_order_id'];
        }
        return $result;
    }

    public function directRequest($sdf)
    {
        // 电子面单预取号
        $preRes = $this->directRequestPre($sdf, $preParams);
        if (!$preParams['ewaybill_order_id']) {
            $result = ['rsp' => 'fail', 'msg' => $preRes['err_msg']];
            $returnResult = $this->backToResult($preRes, $result, $sdf['delivery']);
            return $returnResult;
        }

        $this->title = '微信视频号-获取电子面单';
        $this->timeOut = 20;
        $this->primaryBn = $sdf['primary_bn'];

        # 支持的类型，陆续更新中，枚举值详情请参考order_type，默认为1，加盟型可以不填
        $preParams['order_type'] = $sdf['product_type'];
        # 加上揽件时间
        if (!empty($sdf['pickup_time']) && $sdf['pickup_time'] != 'now') {
            $pickup = [
                'delivery_type' => 1,
            ];
            $begin_time = strtotime(date('Y-m-d') . ' ' . $sdf['pickup_time'] . ':00');
            $end_time = $begin_time + 3600;
            # 预约上门开始时间，秒级时间戳，delivery_type=1时必填
            $pickup['collected_time_begin'] = $begin_time;
            # 预约上门结束时间，秒级时间戳，delivery_type=1时必填
            $pickup['collected_time_end'] = $end_time;

            $preParams['delivery_info'] = json_encode($pickup);  // 预约上门取件、子母件等发货信息字段
        }

        $result = $this->requestCall(STORE_WAYBILL_GET, $preParams, array());
        # 设置物流公司编码
        $sdf['delivery']['logistics_code'] = $preParams['delivery_id'];
        # 记录发件人信息
        $sdf['sender_info'] = $preParams['sender'];
        # 保存结果
        return $this->backToResult($sdf['channel_id'] ?? null, $result, $sdf);
    }

    private function backToResult($channel_id, $ret, $sdf)
    {
        $result = array();
        $waybill = empty($ret['data']) ? array() : json_decode($ret['data'], true);
        # 发货单信息
        $delivery = $sdf['delivery'];
        if (empty($waybill) || $ret['rsp'] == 'fail') {
            $result[] = [
                'succ' => false,
                'msg' => !empty($ret['msg']) ? $ret['msg'] : '获取电子面单失败',
                'delivery_id' => $delivery['delivery_id'],
                'delivery_bn' => $delivery['delivery_bn'],
            ];
            return $result;
        }
        # 解密打印数据
        if (!empty($waybill['print_info'])) {
            $waybill['print_info'] = urldecode(base64_decode($waybill['print_info']));
        }
        # 转换发货人信息
        if (!empty($sdf['sender_info'])) {
            $sdf['sender_info'] = json_decode($sdf['sender_info'], true);
        }
        $result[] = array(
            'succ' => $waybill['waybill_id'] ? true : false,
            'msg' => '',
            'delivery_id' => $delivery['delivery_id'],
            'delivery_bn' => $delivery['delivery_bn'],
            'logi_no' => $waybill['waybill_id'],
            'logistics_code' => $delivery['logistics_code'],
            'mailno_barcode' => '',
            'qrcode' => '',
            'position' => '',
            'position_no' => '',
            'package_wdjc' => '',
            'package_wd' => '',
            'print_config' => json_encode(['ewaybill_order_id' => $waybill['ewaybill_order_id']]),
            'json_packet' => is_array($waybill) ? json_encode($waybill, JSON_UNESCAPED_UNICODE) : $waybill,
            'send_info' => $sdf['sender_info'],
            'customer_code' => $sdf['customer_code'],         // 客户编码
            'monthly_account' => $sdf['monthly_account'],     // 月结号
            'monthly_type' => $sdf['monthly_type'],           // 月结号类型
            'product_type' => $sdf['product_type'],           // 服务类型
            'pickup_time' => $sdf['pickup_time'],             // 上门取件时间
            'seller_mobile' => $sdf['sender_info']['mobile'], // 发件人手机号
        );
        if ($ret['err_msg'] || $ret['rsp'] == 'fail') {
            # 错误信息
            $err_msg = $ret['err_msg'];
            # 电子面单取号失败，请参考delivery_error_msg字段后重试
            if (ome_func::contains($err_msg, '参考delivery_error_msg')) {
                $err_msg = $ret['data']['delivery_error_msg'] ?? $ret['data']['errmsg'];
            }
            $result[] = array(
                'succ' => false,
                'msg' => $err_msg,
                'delivery_id' => $delivery['delivery_id'],
                'delivery_bn' => $delivery['delivery_bn'],
            );
        }
        $this->directDataProcess($result, $channel_id);
        return $result;
    }

    /**
     * 取消电子面单
     * @param $waybillNumber
     * @param $delivery_bn
     * @return mixed
     */
    public function recycleWaybillNew($sdf)
    {
        $waybillModel = app::get('logisticsmanager')->model('waybill');
        $waybillExtendModel = app::get('logisticsmanager')->model('waybill_extend');
        # 更新状态
        $waybillModel->update(array('status' => 2, 'create_time' => time()), array('id' => $sdf['waybill_id']));

        $this->primaryBn = empty($sdf['delivery_bn']) ? $sdf['waybill_number'] : $sdf['delivery_bn'];
        $this->title = '微信视频号_' . $sdf['company_code'] . '取消电子面单';

        # 电子面单扩展信息
        $extendInfo = $waybillExtendModel->db_dump(['waybill_id' => $sdf['waybill_id']], 'print_config');
        $extendInfo['print_config'] && $extendInfo['print_config'] = json_decode($extendInfo['print_config'], 1);
        # 参数
        $params = array(
            'ewaybill_order_id' => $extendInfo['print_config']['ewaybill_order_id'] ?? '', // 电子面单订单id，全局唯一id
            'delivery_id' => $sdf['company_code'], // 快递公司id
            'logistics_no' => $sdf['waybill_number'], // 快递单号

        );
        $callback = array(
            'class' => get_class($this),
            'method' => 'callback',
            'params' => $sdf,
        );
        # 设置拦截回传
        if (!empty($sdf['interception_id'])) {
            $callback['method'] = 'intercetioncancelBack';
        }
        return $this->requestCall(STORE_WAYBILL_CANCEL, $params, $callback);
    }

    public function getWaybillISearch($sdf = array())
    {
        $params = [
            // 'need_balance' => true, // 必填, 因为矩阵默认是true，所以不传
            'limit' => 50, // 必填
            'delivery_id' => $sdf['delivery_id'],
            'status' => 3, // 1 绑定审核中;2 取消绑定审核中;3 已绑定;4 已解除绑定;5 绑定未通过;6 取消绑定未通过
            'ewaybill_acct_id' => $sdf['acct_id'],
        ];

        $title = '查询开通的网点账号信息';

        $result = $this->requestCall(STORE_WAYBILL_ADRESS, $params, array());
        return $result;
    }
}
