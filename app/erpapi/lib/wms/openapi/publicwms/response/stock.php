<?php

class erpapi_wms_openapi_publicwms_response_stock extends erpapi_wms_response_stock
{

    /**
     * wms.stock.update
     *
     **/
    public function update($params)
    {
        $this->__apilog['title'] = $this->__channelObj->wms['channel_name'] . '库存更新';
        $this->__apilog['original_bn'] = $params['stock_sn'];

        $data = array();
        $data['stock_sn'] = $params['stock_sn'];
        $data['operate_time'] = strtotime($params['operate_time']);
        $data['stock_type'] = $params['stock_type']; // 1 全量， 2 增量
        $items = json_decode($params['item'], true);
        if (empty($items)) {
            $this->__apilog['result']['msg'] = 'sku库存列表为空';
            return false;
        }

        $mwmsObj = kernel::single('material_wms');
        $brand_sku_code = $stockList = [];
        foreach ($items as $item) {
            $quantity_list = $item['quantity_list'];
            if (empty($quantity_list)) {
                $this->__apilog['result']['msg'] = 'sku品牌编码[' . $item['brand_sku_code'] . ']的库存列表为空';
                return false;
            }

            $brand_sku_code[] = $item['brand_sku_code'];
            foreach ($quantity_list as $row) {
                $stockList[] = array(
                    'brand_sku_code' => $row['brand_sku_code'],
                    'org_no' => $row['village_code'] ?? '',
                    'store' => $row['actual_quantity'],
                    'lock_store' => $row['freeze_quantity'] ?? 0,
                    'actual_quantity' => $row['actual_quantity'],
                    'available_quantity' => $row['available_quantity'],
                );
            }
        }

        if (empty($stockList)) {
            $this->__apilog['result']['msg'] = '同步的库存结果集为空';
            return false;
        }

        # 根据品牌编码读取基础物料
        $error_msg = '';
        $brand_sku_list = $mwmsObj->getMaterialByBrandSkuCode($brand_sku_code, $error_msg);
        if (false === $brand_sku_list) {
            $this->__apilog['result']['msg'] = $error_msg;
            return false;
        }

        foreach ($stockList as $item) {
            # 根据sku品牌编码读取关联的基础物料列表
            $materialList = $brand_sku_list[$item['brand_sku_code']] ?? [];
            if (empty($materialList)) {
                continue;
            }

            foreach ($materialList as $material) {
                # 如果不存在小镇编码，或者小镇编码一致的情况
                if (empty($item['org_no']) || (!empty($item['org_no']) && $material['org_no'] == $item['org_no'])) {
                    $data['items'][] = [
                        'store_bn' => $params['store_bn'],
                        'bm_id' => $material['bm_id'],
                        'spu_id' => $material['material_spu'],
                        'material_bn' => $material['material_bn'],
                        'quantity' => $item['store'],     // 可用库存
                        'lock_quantity' => $item['lock_store'],  // 锁定库存
                        'available_quantity' => $item['available_quantity'],
                        'branch_id' => $material['branch_id'],
                        'store_id' => $material['store_id'],
                    ];
                }
            }
        }
        return $data;
    }
}
