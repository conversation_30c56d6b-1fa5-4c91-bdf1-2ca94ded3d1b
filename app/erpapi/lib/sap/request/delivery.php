<?php
/**
 * Created by PhpStorm.
 * User: sqs
 * Date: 2022/7/15
 * Time: 6:05 PM
 */


class erpapi_sap_request_delivery extends erpapi_sap_request_abstract
{
    /**
     * 推送类型 - 发货单
     * @var string
     */
    protected $__push_type = 'delivery';

    public function push($sdf)
    {
        $rsaObj = kernel::single('erpapi_sap_rsa');
        $oOperation_log = app::get('ome')->model('operation_log');
        $orderObj = kernel::single('ome_order');
        $modelOrders = app::get('ome')->model('orders');
        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $reshipMdl = app::get('ome')->model('reship');
        $shopMdl = app::get('ome')->model('shop');

        # 时间检测
        $check_push = $this->check_push_time($this->__push_type, $sdf['delivery']['delivery_id']);
        if ($check_push) {
            return $this->error('单据正在处理，请稍候再试');
        }

        # 设置推送时间
        $shopInfo = $shopMdl->dump(array('shop_id' => $sdf['delivery']['shop_id']), 'start_order_time');
        if (!empty($shopInfo['start_order_time'])) {
            $this->__push_time = $shopInfo['start_order_time'];
        }

        # 判断历史推送状态，不能重复推送
        $sapOrderInfo = $sapOrderMdl->dump(array('bill_id' => $sdf['delivery']['delivery_id'], 'bill_type' => $this->__push_type), '*');
        if (!empty($sapOrderInfo) && $sapOrderInfo['sync_status'] == 'succ') {
            $msg = '该单据（类型：' . $sapOrderInfo['bill_type'] . '）已经推送过了，不能重复推送';
            $oOperation_log->write_log('sap_delivery@ome', $sapOrderInfo['sap_id'], $msg);
            return $this->error($msg);
        }

        $change_order_id = $order_paytime = [];
        # 是否历史订单
        $is_history = $this->is_history_order($sdf['delivery']);
        # 来源订单ID
        foreach ($sdf['orders'] as $k => $order) {
            # 原始订单为0元抽奖订单，不推送esb
            if (!empty($order['order']['is_lucky_flag'])) {
                unset($sdf['orders'][$k]);
                continue;
            }

            # 判断订单是否为换货单，换货单不推送sap
            $is_change = $reshipMdl->is_change_order($order['order']['order_id']);
            if ($is_change) {
                $change_order_id[] = $order['order']['order_id'];
                unset($sdf['orders'][$k]);
                continue;
            }

            # 记录订单支付时间
            if (!empty($order['order']['paytime'])) {
                $order_paytime[] = $order['order']['paytime'];
            }

            # 获取原单号
            $sourceOrderBn = $orderObj->getSourceOrder($order['order']);
            $sourceOrderBns[] = $sourceOrderBn;
        }

        # 只推送指定日期的发货单
        if (!empty($order_paytime) && !empty($this->__push_time)) {
            $min_paytime = count($order_paytime) > 1 ? min($order_paytime) : current($order_paytime);
            if ($min_paytime < $this->__push_time && !$is_history) {
                return $this->error('此业务只推送日期【' . date('Y-m-d H:i:s', $this->__push_time) . '】之后的发货单数据');
            }
        }

        # 获取原始订单ID
        if (!empty($sourceOrderBns)) {
            $orderList = $modelOrders->getList('order_id', array('order_bn' => $sourceOrderBns));
            $sdf['delivery']['source_order_id'] = array_column($orderList, 'order_id');
        }

        # 保存中间表主表
        if (empty($sapOrderInfo) && !empty($sdf['orders'])) {
            $sapOrderInfo = $this->saveSapMainData($sdf, $this->__push_type);
        }

        try {
            # 换出订单不推送
            if (!empty($change_order_id) && empty($sdf['orders'])) {
                throw new Exception('换出订单发货单不推送SAP');
            }

            # 其他扩展参数
            $sdf['ext']['sap_data'] = $sapOrderInfo;
            //params
            $params = $this->_format_params($sdf, $error_msg);
            if (!$params) {
                throw new Exception('数据本地验证失败,(' . $error_msg . ')');
            }

            # 0元订单状态变成无需推送
            if (isset($params['total_pay_amount']) && floatval($params['total_pay_amount']) <= 0) {
                $updateSap = [
                    'sync_status' => 'none',
                    'sync_time' => time()
                ];
                $sapOrderMdl->update($updateSap, array('sap_id' => $sapOrderInfo['sap_id']));
                # 合并修改数据
                $sapOrderInfo = array_merge($sapOrderInfo, $updateSap);
                # 删除无用字段
                unset($params['total_pay_amount']);
            }

            # 检查数据
            $order_data = json_decode($params['data'], true);
            # 扩展参数
            $order_data['extend'] = [
                'delivery_id' => $sdf['delivery']['delivery_id'],
                'orders' => $sdf['orders'],
                'shop_type' => $sdf['delivery']['shop_type'],
                'is_history' => $is_history,
                'delivery_items' => $sdf['delivery']['delivery_items'],
            ];
            $is_check = $this->_check_params($order_data, $error_msg);
            if (!$is_check) {
                throw new Exception('数据本地验证失败,(' . $error_msg . ')');
            }
        } catch (Exception $ex) {
            $msg = $ex->getMessage();
            $oOperation_log->write_log('sap_delivery@ome', $sapOrderInfo['sap_id'], $msg);
            parent::_log_sync_status($sapOrderInfo['sap_id'], null, ['rsp' => 'fail', 'msg' => $msg]);
            return $this->error($msg);
        }

        # 检查推送开关
        if ($this->check_push_sap() == 'OFF') {
            # 记录日志
            $oOperation_log->write_log('sap_delivery@ome', $sapOrderInfo['sap_id'], '未开启推送开关，暂不推送');
            return $this->succ('success');
        }

        # 历史发货单无需推送
        if ($sapOrderInfo['sync_status'] == 'none' || $is_history) {
            $oOperation_log->write_log('sap_delivery@ome', $sapOrderInfo['sap_id'], '该发货单无需推送esb');
            return $this->succ('success');
        }

        # data加密
        if (!empty($params['data'])) {
            $params['data'] = $rsaObj->rsa_encode($sdf['delivery']['shop_type'], $params['data']);
        }
        # 签名
        $params['sign'] = $rsaObj->gen_sign($sdf['delivery']['shop_type'], $params);

        $this->__original_bn = $sdf['delivery']['delivery_bn'];
        $title = '发货单推送SAP接口';
        # 请求接口
        $callback = array();
        $count = 0;
        $current_time = time();

        do {
            $response = $this->__caller->call(SAP_PUSH_SALES, $params, $callback, $title, 30, $this->__original_bn, true);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['err_msg'])) {
                break;
            }
            $count++;

        } while ($count < 3);

        # 记录推送状态
        parent::_log_sync_status($sapOrderInfo['sap_id'], $current_time, $response);
        # 记录操作日志
        $memo = $title . '：' . $response['rsp'];
        if ($response['rsp'] == 'fail') {
            $memo .= '，原因：' . $response['err_msg'];
        }
        $oOperation_log->write_log('sap_delivery@ome', $sapOrderInfo['sap_id'], $memo);
        return $response;
    }

    /**
     * 销售单请求参数
     * @param $sdf
     * @param $error_msg
     * @return void
     */
    public function _format_params($sdf, &$error_msg)
    {
        if (empty($sdf['delivery']['delivery_items'])) {
            $error_msg = '发货单明细不存在';
            return false;
        }

        $shop_type = $sdf['delivery']['shop_type'];
        # appid
        $this->__app_id = $this->getAppId($shop_type);
        if (empty($this->__app_id)) {
            $error_msg = '未找到店铺类型为"' . $sdf['delivery']['shop_type'] . '"的appId映射关系';
            return false;
        }

        $total_pay_amount = 0;
        $data = $error_list = [];
        $result = ['order_id' => [], 'oid' => [], 'source_oid' => [], 'coupon_id' => [], 'dly_product_id' => []];

        $orderItemsMdl = app::get('ome')->model('order_items');
        # 数据类
        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($shop_type)));

        # 获取发货sku明细
        foreach ($sdf['delivery']['delivery_items'] as $item) {
            $result['dly_product_id'][] = $item['product_id'];
        }

        foreach ($sdf['orders'] as $row) {
            # 订单ID
            $order_id = $row['order']['order_id'];
            $result['order_id'][] = $order_id;
            foreach ($row['order']['order_objects'] as $object) {
                if ($object['delete'] == 'true') {
                    continue;
                }

                # 订单明细
                $orderItemList = $orderItemsMdl->getList('*', array('order_id' => $order_id, 'obj_id' => $object['obj_id'], 'delete' => 'false'));
                if (empty($orderItemList)) {
                    continue;
                }

                # 记录已发货的商品明细
                foreach ($orderItemList as $item) {
                    # 过滤未发货的商品明细
                    if ($item['sendnum'] <= 0 || !in_array($item['product_id'], $result['dly_product_id'])) {
                        continue;
                    }

                    # 获取子单号
                    $oid = $platformObj->getOid($order_id, $object['oid']);
                    # 记录oid
                    $result['oid'][] = $oid;
                    $result['source_oid'][] = $object['oid'];
                    # 获取基础物料
                    $bmInfo = $this->_getBMStoreAndOrg($item['bn'], true);
                    if (empty($bmInfo['material_spu_id'])) {
                        $error_list[] = '商品大码不能为空';
                    }
                    if (empty($bmInfo['org_no'])) {
                        $error_list[] = '门店id不能为空';
                    }
                    if (empty($bmInfo['store_bn'])) {
                        $error_list[] = '商户id不能为空';
                    }
                    if (empty($bmInfo['material_bn'])) {
                        $error_list[] = '商品SKU信息不能为空';
                    }


                    # 扩展参数
                    $extend_data = [
                        'shop_type' => $shop_type,
                        'order_source' => $row['order_source_oid'][$oid] ?? null,
                    ];
                    $shop_store_bn = $bmInfo['store_bn'];

                    $fulfillment_store = $this->_getfulfillment_store($order_id,$bmInfo['material_bn']);
                    if($fulfillment_store){
                        $shop_store_bn = $fulfillment_store['store_bn'];
                    }

                    # 订单信息
                    $orderHead = [
                        'orderSource' => $this->getOrderSource($shop_type, $extend_data),
                        'orderType' => 'SHIPPED', // 固定值
                        'orderSn' => $row['order']['order_bn'],  // 订单编号
                        'eshopOrderSn' => $oid, // 业务订单编号
                        'mallId' => $bmInfo['org_no'], // 门店Id
                        'shopId' => $shop_store_bn, // 商户id
                        'spuId' => $bmInfo['material_spu_id'], // 商品大码
                        'productId' => $item['shop_goods_id'] ?? '', // 平台商品ID
                        'skuId' => $item['shop_product_id'] ?? '', // 平台SKU ID
                        'vipId' => $row['order']['card_number'] ?? '', // 会员卡号
                        'orderTime' => $row['order']['paytime'] . '000', // 订单下单/退单时间 长度：13位
                        'orderPrice' => 0, // 订单总金额
                        'discAmount' => 0, // 订单优惠金额
                        'payAmount' => 0, // 订单实付金额
                        'transAmount' => 0, // 运费
                        'bankAmount' => 0, // 银行手续费
                        'oriFlowNo' => '', // 流水号
                    ];
                    $data['orderItems'][] = $orderHead;

                    $orderItem = [
                        'eshopOrderSn' => $oid, // 在线商城业务订单编号
                        'spuId' => $bmInfo['material_spu_id'], // 商品大码
                        'sku' => $bmInfo['material_bn'], // 商品SKU信息
                        'count' => intval($item['sendnum']), // 订单购买商品数量
                        'unitPrice' => 0, // 商品单价
                        'totalPrice' => 0, // 商品总价
                        'mallId' => $bmInfo['org_no'], // 门店Id
                    ];
                    $data['goodItems'][] = $orderItem;
                }
            }
        }

        # 错误信息
        if (!empty($error_list)) {
            $error_msg = implode(';', $error_list);
            return false;
        }

        $source_order_id = $sdf['delivery']['source_order_id'] ?? $result['order_id'];
        # 获取优惠字段
        $discount_fields = kernel::single(sprintf('erpapi_sap_mapping_platform_%s', erpapi_sap_func::getShopType($shop_type)))->getDiscountFields();
        # 计算订单支付信息中的oid金额总和
        $paymentsOidList = ['payment' => [], 'discount' => []];
        # 获取订单支付信息
        $payments = $this->_getSouceOrderPayments($source_order_id, array_unique($result['oid']));
        if (empty($payments)) {
            $error_msg = '获取原单支付明细失败';
            return false;
        }

        # 获取优惠券code
        foreach ($payments as $k => $payment) {
            $key = $payment['eshopOrderSn'];
            if (empty($key)) {
                continue;
            }

            # 记录优惠券code
            if (!empty($payment['couponCode'])) {
                $result['coupon_id'][] = $payment['couponCode'];
            }
            # 计算订单支付明细金额之和
            $paymentsOidList['payment'][$key] = bcadd($paymentsOidList['payment'][$key], $payment['amount'], 2);
            # 优惠类型
            if (!empty($discount_fields) && in_array($payment['addon']['sub_type'], $discount_fields)) {
                $paymentsOidList['discount'][$key] = bcadd($paymentsOidList['discount'][$key], $payment['amount'], 2);
            }
            # 删除扩展字段
            unset($payments[$k]['addon']);
        }
        $data['paymentItems'] = $payments;

        # 更新订单明细中的金额
        foreach ($data['orderItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['payAmount'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['orderItems'][$k]['orderPrice'] = $data['orderItems'][$k]['payAmount'];
            }
            if (!empty($paymentsOidList['discount'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['discAmount'] = floatval($paymentsOidList['discount'][$item['eshopOrderSn']]);
            }
            # 记录订单的总实付金额
            $total_pay_amount = bcadd($total_pay_amount, $data['orderItems'][$k]['payAmount'], 2);
        }
        # 更新sku明细中的支付金额
        foreach ($data['goodItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['goodItems'][$k]['totalPrice'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['goodItems'][$k]['unitPrice'] = floatval(bcdiv($data['goodItems'][$k]['totalPrice'], $item['count'], 2));
            }
        }

        # 获取订单优惠券信息
        $coupons = $this->_getSourceOrderCoupons($source_order_id, $shop_type, $result['coupon_id']);
        if (!empty($coupons)) {
            $data['couponItems'] = $coupons;
        }

        # 保存中间表的明细
        $this->_saveSapItemsData($data, $sdf['ext']['sap_data']);

        # 删除扩展字段
        foreach ($data['couponItems'] as $k => $item) {
            unset($data['couponItems'][$k]['eshopOrderSn']);
        }

        # 验证订单是否推送
        $order_pushed = $this->check_push_order($source_order_id);
        if (!$order_pushed) {
            $error_msg = '原始支付订单未成功推送';
            return false;
        }

        $params = [
            'appId' => $this->__app_id,
            'timestamp' => erpapi_sap_func::mixtimestamp(true),
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'total_pay_amount' => $total_pay_amount,
        ];
        return $params;
    }

    /**
     * 保存到sap中间数据表
     * @param $params
     * @return void
     */
    protected function saveSapData($sdf, $params, $bill_type, &$error_msg)
    {
        if (empty($params) || empty($bill_type)) {
            return false;
        }

        $order_data = [
            'bill_id' => $sdf['delivery']['delivery_id'],
            'bill_no' => $sdf['delivery']['delivery_bn'],
            'bill_type' => $bill_type,
            'shop_id' => $sdf['delivery']['shop_id'],
            'shop_type' => $sdf['delivery']['shop_type'],
            'create_time' => time()
        ];
        # 保存原始订单ID
        if (!empty($sdf['delivery']['source_order_id'])) {
            $order_data['source_bill_id'] = implode(',', $sdf['delivery']['source_order_id']);
        }
        $result = parent::_saveSapOrders($order_data, $params, $error_msg);
        return $result;
    }

    /**
     * 保存到sap中间数据表 - 主数据
     * @param $sdf
     * @return mixed
     */
    protected function saveSapMainData($sdf, $bill_type)
    {
        $orderMdl = app::get('sales')->model('sap_orders');

        $order_data = [
            'bill_id' => $sdf['delivery']['delivery_id'],
            'bill_no' => $sdf['delivery']['delivery_bn'],
            'bill_type' => $bill_type,
            'shop_id' => $sdf['delivery']['shop_id'],
            'shop_type' => $sdf['delivery']['shop_type'],
            'create_time' => time()
        ];
        # 来源订单ID
        if (!empty($sdf['delivery']['source_order_id'])) {
            $order_data['source_bill_id'] = implode(',', $sdf['delivery']['source_order_id']);
        } else {
            foreach ($sdf['orders'] as $order) {
                $source_bill_id[] = $order['order']['order_id'];
            }
            if (!empty($source_bill_id)) {
                $order_data['source_bill_id'] = implode(',', $source_bill_id);
            }
        }
        # 历史发货单无需推送
        if ($this->is_history_order($sdf['delivery'])) {
            $order_data['sync_status'] = 'none';
        }
        $result = $orderMdl->save($order_data);
        if (!$result) {
            return false;
        }
        return $order_data;
    }

    /**
     * 数据校验
     * @param $params
     * @param $error_msg
     * @return bool
     */
    protected function _check_params($params, &$error_msg)
    {
        if (isset($params['extend']) && $params['extend']['is_history']) {
            return true;
        }

        foreach ($params['delivery_items'] as $item) {
            if (bccomp($item['sendnum'], $item['number']) < 0) {
                $error_msg = '物料[' . $item['bn'] . ']还未发货完毕';
                return false;
            }
        }

        # 检查参数
        $result = parent::_check_params($params, $new_msg);
        if (!$result) {
            $error_msg = $new_msg;
            return false;
        }

        # 微信商城增加过滤的金额
        if ($params['extend']['shop_type'] == 'ecos.ecshopx') {
            $couponMdl = app::get('ome')->model('order_coupon_ecshopx');
            $notPushData = ['platform_type' => [], 'coupon_type' => []];
            $params['extend']['discount_list'] = [];
            # 店铺类型转换
            $shop_type = kernel::single('erpapi_sap_func')->getShopType($params['extend']['shop_type']);
            # 获取无需推送esb的优惠字段列表
            $notPushList = kernel::single('ome_sap_data_platform_' . $shop_type)->getNotPushCouponTypeList();
            if (!empty($notPushList)) {
                foreach ($notPushList as $item) {
                    list($platform_type, $coupon_type) = explode('@', $item);
                    if (!in_array($platform_type, $notPushData['platform_type'])) {
                        $notPushData['platform_type'][] = $platform_type;
                    }
                    if (!in_array($coupon_type, $notPushData['coupon_type'])) {
                        $notPushData['coupon_type'][] = $coupon_type;
                    }
                }
            }
            # 查询其他优惠
            $filter = [];
            if (!empty($notPushData['platform_type'])) {
                $filter['platform_type|in'] = $notPushData['platform_type'];
            }
            if (!empty($notPushData['coupon_type'])) {
                $filter['coupon_type|in'] = $notPushData['coupon_type'];
            }
            if (!empty($filter)) {
                foreach ($params['extend']['orders'] as $order) {
                    # 增加订单ID查询条件
                    $filter['order_id'] = $order['order']['order_id'];
                    $otherList = $couponMdl->getList('oid,coupon_amount', $filter);
                    if (!empty($otherList)) {
                        # 刷新子单号
                        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', $shop_type));
                        foreach ($otherList as $key => $item) {
                            $otherList[$key]['oid'] = $platformObj->getOid($order['order']['order_id'], $item['oid']);
                        }
                        $otherData = array_column($otherList, null, 'oid');
                        # 设置已过滤的金额，用于后续金额的判断
                        foreach ($otherData as $item) {
                            if (empty($params['extend']['discount_list'][$item['oid']])) {
                                $pay_item = ["oid" => $item['oid'], "amount" => $item['coupon_amount']];
                                $params['extend']['discount_list'][$item['oid']] = $pay_item;
                            } else {
                                $amount = bcadd($params['extend']['discount_list'][$item['oid']]['amount'], $item['coupon_amount'], 2);
                                $params['extend']['discount_list'][$item['oid']]['amount'] = $amount;
                            }
                        }
                    }
                }
            }
        }
        return $this->_check_delivery_params($params, $error_msg);
    }

    public function onlyPush($sap_id, $sdf)
    {
        $this->__original_bn = $sdf['orderItems'][0]['orderSn'];
        $title = '发货订单推送SAP接口';
        # 请求接口
        $callback = array();
        $count = 0;
        $current_time = time();
        $oOperation_log = app::get('ome')->model('operation_log');

        do {
            $response = $this->__caller->call(SAP_PUSH_SALES, $sdf, $callback, $title, 30, $this->__original_bn, true);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['msg'])) {
                break;
            }
            $count++;
        } while ($count < 3);

        # 记录推送状态
        parent::_log_sync_status($sap_id, $current_time, $response);
        # 记录操作日志
        $memo = $title . '：' . $response['rsp'];
        if ($response['rsp'] == 'fail') {
            $memo .= '，原因：' . $response['msg'];
        }
        $oOperation_log->write_log('sap_delivery@ome', $sap_id, $memo);
        return $response;
    }
}