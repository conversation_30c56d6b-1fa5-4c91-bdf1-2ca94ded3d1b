<?php
/**
 * Created by PhpStorm.
 * User: sqs
 * Date: 2022/7/15
 * Time: 6:05 PM
 */


class erpapi_sap_request_reship extends erpapi_sap_request_abstract
{
    protected $__push_type = 'return';

    public function push($sdf)
    {
        $rsaObj = kernel::single('erpapi_sap_rsa');
        $oOperation_log = app::get('ome')->model('operation_log');
        $orderObj = kernel::single('ome_order');
        $modelOrders = app::get('ome')->model('orders');
        $sapOrderMdl = app::get('sales')->model('sap_orders');

        # 时间检测
        $check_push = $this->check_push_time($this->__push_type, $sdf['reship']['reship_id']);
        if ($check_push) {
            return $this->error('单据正在处理，请稍候再试');
        }

        # 部分退款标识不推送esb
//        if (!empty($sdf['reship']['is_part_refund']) && $sdf['reship']['is_part_refund'] == '1') {
//            return $this->error('订单存在部分退款不推送esb');
//        }

        # 原始订单为0元抽奖订单，不推送esb
        if (!empty($sdf['order']['is_lucky_flag'])) {
            return $this->error('原始订单为0元抽奖订单，不推送esb');
        }

        # 是否历史订单
        $is_history = $this->is_history_order($sdf['reship']);
        # 判断历史推送状态，不能重复推送
        $sapOrderInfo = $sapOrderMdl->dump(array('bill_id' => $sdf['reship']['reship_id'], 'bill_type' => $this->__push_type), '*');
        if (!empty($sapOrderInfo) && $sapOrderInfo['sync_status'] == 'succ') {
            $msg = '该单据（类型：' . $sapOrderInfo['bill_type'] . '）已经推送过了，不能重复推送';
            $oOperation_log->write_log('sap_reship@ome', $sapOrderInfo['sap_id'], $msg);
            return $this->error($msg);
        }

        # 判断是否存在来源订单号
        if (!empty($sdf['order']['relate_order_bn'])) {
            $sourceOrderBn = $orderObj->getSourceOrder($sdf['order']);
            if (!empty($sourceOrderBn)) {
                $orderInfo = $modelOrders->dump(array('order_bn' => $sourceOrderBn), 'order_id,order_bn');
                $sdf['reship']['source_order_id'][] = $orderInfo['order_id'];
            }
        }

        # 保存中间表主表
        $sdf['sap_id'] = $sapOrderInfo['sap_id'];
        $sapOrderInfo = $this->saveSapMainData($sdf, $this->__push_type);

        try {
            # 其他扩展参数
            $sdf['ext']['sap_data'] = $sapOrderInfo;
            $sdf['ext']['is_history'] = $is_history;
            $sdf['ext']['order_is_history'] = $this->is_history_order($sdf['order']);

            # 商城单独处理
            if ($sdf['order']['shop_type'] == 'ecos.ecshopx') {
                $params = $this->_format_ecshopx_params($sdf, $error_msg);
            } else {
                $params = $this->_format_params($sdf, $error_msg);
            }

            if (!$params) {
                throw new Exception('数据本地验证失败,(' . $error_msg . ')');
            }

            # 0元订单状态变成无需推送
            if (isset($params['total_pay_amount']) && floatval($params['total_pay_amount']) <= 0) {
                $updateSap = [
                    'sync_status' => 'none',
                    'sync_time' => time()
                ];
                $sapOrderMdl->update($updateSap, array('sap_id' => $sapOrderInfo['sap_id']));
                # 合并修改数据
                $sapOrderInfo = array_merge($sapOrderInfo, $updateSap);
                # 删除无用字段
                unset($params['total_pay_amount']);
            }

            # 检查数据
            $order_data = json_decode($params['data'], true);
            # 扩展参数
            $order_data['extend'] = [
                'order_id' => $sdf['order']['order_id'],
                'source_order_id' => empty($sdf['reship']['source_order_id']) ? $sdf['order']['order_id'] : $sdf['reship']['source_order_id'],
                'shop_type' => $sdf['order']['shop_type'],
                'shop_id' => $sdf['order']['shop_id'],
                'is_history' => $is_history,
            ];
            $is_check = $this->_check_params($order_data, $error_msg);
            if (!$is_check) {
                throw new Exception('数据本地验证失败,(' . $error_msg . ')');
            }

            # 替换历史子单号
            /*if ($sdf['ext']['order_is_history'] && !empty($params['oid_mapping'])) {
                foreach ($order_data['orderItems'] as $key => $item) {
                    if (empty($params['oid_mapping'][$item['oriFlowNo']])) {
                        continue;
                    }
                    $order_data['orderItems'][$key]['oriFlowNo'] = $params['oid_mapping'][$item['oriFlowNo']];
                }
                # 替换data参数
                unset($params['oid_mapping'], $order_data['$order_data']);
                $params['data'] = json_encode($order_data, JSON_UNESCAPED_UNICODE);
            }*/
        } catch (Exception $ex) {
            $msg = $ex->getMessage();
            $oOperation_log->write_log('sap_reship@ome', $sapOrderInfo['sap_id'], $msg);
            parent::_log_sync_status($sapOrderInfo['sap_id'], null, ['rsp' => 'fail', 'msg' => $msg]);
            return $this->error($msg);
        }

        # 检查推送开关
        if ($this->check_push_sap() == 'OFF') {
            # 记录日志
            $oOperation_log->write_log('sap_reship@ome', $sapOrderInfo['sap_id'], '未开启推送开关，暂不推送');
            return $this->succ('success');
        }

        # 历史退货单无需推送
        if ($sapOrderInfo['sync_status'] == 'none' || $is_history) {
            $oOperation_log->write_log('sap_reship@ome', $sapOrderInfo['sap_id'], '该退货单无需推送esb');
            return $this->succ('success');
        }

        if (!empty($params['data'])) {
            # data加密
            $params['data'] = $rsaObj->rsa_encode($sdf['reship']['shop_type'], $params['data']);
        }
        # 签名
        $params['sign'] = $rsaObj->gen_sign($sdf['reship']['shop_type'], $params);

        $this->__original_bn = $sdf['order']['order_bn'];
        $title = '退货单推送SAP接口';
        # 请求接口
        $callback = array();
        $count = 0;
        $current_time = time();

        do {
            $response = $this->__caller->call(SAP_PUSH_SALES, $params, $callback, $title, 30, $this->__original_bn, true);
            if (in_array($response['rsp'], array('succ', 'success'))) {
                break;
            } elseif ($response['rsp'] == 'fail' && !empty($response['err_msg'])) {
                break;
            }

            $count++;
        } while ($count < 3);

        # 记录推送状态
        parent::_log_sync_status($sapOrderInfo['sap_id'], $current_time, $response);
        # 记录操作日志
        $memo = $title . '：' . $response['rsp'];
        if ($response['rsp'] == 'fail') {
            $memo .= '，原因：' . $response['err_msg'];
        }
        $oOperation_log->write_log('sap_reship@ome', $sapOrderInfo['sap_id'], $memo);
        return $response;
    }

    /**
     * 读取是否存在未推送esb的退换货单
     * @param $order_id
     * @param $reship_id
     * @return mixed
     */
    private function _getNoPushReshipListByOrderId($order_id, $reship_id)
    {
        if (empty($order_id) || empty($reship_id)) {
            return [];
        }

        # 读取其他退换货单
        $sql = "select reship_id,reship_bn,shop_id from sdb_ome_reship WHERE order_id = {$order_id} AND reship_id != {$reship_id} AND is_check = '7'";
        $reshipList = kernel::database()->select($sql);
        if (empty($reshipList)) {
            return [];
        }

        $sapOrderMdl = app::get('sales')->model('sap_orders');
        $result = [];

        foreach ($reshipList as $item) {
            $filter = [
                'shop_id' => $item['shop_id'],
                'bill_id' => $item['reship_id'],
                'bill_type' => $this->__push_type
            ];
            $sapOrders = $sapOrderMdl->dump($filter, 'sap_id,sync_status');
            if (empty($sapOrders) || $sapOrders['sync_status'] == 'fail') {
                $result[] = $item['reship_bn'];
            }
        }
        return $result;
    }

    /**
     * 微商城退货单请求参数
     * @param $sdf
     * @param $error_msg
     * @return mixed
     */
    protected function _format_ecshopx_params($sdf, &$error_msg)
    {
        if (empty($sdf['reship']['reship_items'])) {
            $error_msg = '订单明细不存在';
            return false;
        }

        $shop_type = $sdf['reship']['shop_type'];
        # appid
        $this->__app_id = $this->getAppId($shop_type);
        if (empty($this->__app_id)) {
            $error_msg = '未找到店铺类型为"' . $shop_type . '"的appId映射关系';
            return false;
        }

        # 读取是否存在未推送esb的退换货单
//        $noPushReshipList = $this->_getNoPushReshipListByOrderId($sdf['reship']['order_id'], $sdf['reship']['reship_id']);
//        if (!empty($noPushReshipList)) {
//            $error_msg = '以下退换货单[' . implode(',', $noPushReshipList) . ']还未成功推送ESB';
//            return false;
//        }

        $error_list = [];
        $total_pay_amount = $total_discount_amount = 0;
        $data = ['orderItems' => [], 'goodItems' => [], 'paymentItems' => []];
        $result = ['oid' => [], 'source_oid' => [], 'coupon_id' => [], 'return_items' => []];
        # 数据类
        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($shop_type)));

        # 获取换货单的原始订单退入商品明细
        if (!empty($sdf['reship']['source_order_id'])) {
            $result['return_items'] = $this->_getSourceChangeItems($sdf['reship']['order_id'], $sdf['reship']['reship_items']);
        }

        $source_order_id = $sdf['reship']['source_order_id'] ?? $sdf['reship']['order_id'];
        # 获取原始订单推送esb的支付单信息，并将支付和优惠分开
        $sourcePayedInfo = $this->_getSouceOrderDiscountList($sdf['reship']['shop_id'], $shop_type, $source_order_id);
        if (empty($sourcePayedInfo['payed'])) {
            $error_msg = '原始订单的支付信息不能为空';
            return false;
        }
        # 获取其他退货单的已退货数量
        $otherReshipItems = $this->getOtherReshipItems($sdf['reship']['order_id'], $sdf['reship']['reship_id']);

        foreach ($sdf['reship']['reship_items'] as $k => $item) {
            # 换货商品无需处理，只处理退货商品
            if ($item['return_type'] == 'change') {
                break;
            }

            # 判断是否存在关联单号，如果存在，则取原始订单明细的oid
            if (!empty($sdf['reship']['source_order_id'])) {
                # 原换货单对应的退货入库sku
                $source_return_item = $result['return_items'][$item['product_id']];
                if (empty($source_return_item)) {
                    continue;
                }
                $order_item_id = $source_return_item['order_item_id'];
            } else {
                $order_item_id = $item['order_item_id'];
            }
            $sql = "select a.item_id,a.order_id,a.bn,a.name,a.nums,a.return_num,a.item_type,b.oid,a.shop_goods_id,a.shop_product_id"
                . " from sdb_ome_order_items a LEFT JOIN sdb_ome_order_objects b ON a.order_id = b.order_id AND a.obj_id = b.obj_id"
                . " WHERE a.item_id = {$order_item_id}";
            $orderItem = kernel::database()->selectrow($sql);
            if (empty($orderItem)) {
                continue;
            }

            # 获取子单号
            $oid = $platformObj->getOid($orderItem['order_id'], $orderItem['oid']);
            # 获取子单号对应的支付明细
            $paymentList = $sourcePayedInfo['payed'][$oid] ?? [];
            if (empty($paymentList)) {
                $error_msg = '原始订单的支付明细不存在[oid=' . $oid . ']';
                continue;
            }

            # 如果当前sku的退货数量是最后一笔，则取剩余金额，否则取分摊金额
            if (false === $item['is_last_reship']) {
                # 分摊支付明细
                foreach ($paymentList as $key => $payment) {
                    # sku单价
                    $item_price = bcdiv($payment['amount'], $orderItem['nums'], 3);
                    # esb的sku金额
                    $paymentList[$key]['amount'] = bcmul($item_price, $item['num'], 2);
                    # 记录分摊的item_id
                    $paymentList[$key]['split_item_id'] = $payment['item_id'];
                    # 删除多余字段
                    unset($paymentList[$key]['item_id'], $paymentList[$key]['order_id']);
                }
            } else {
                $hasPushReshipData = [];
                # 读取已成功推送esb的退货单总金额
                $sql = "SELECT c.oriFlowNo,b.paymentCode,b.split_item_id,SUM(IF(b.amount<0,0,b.amount)) AS amount FROM sdb_sales_sap_orders a"
                    . " LEFT JOIN sdb_sales_sap_payments b ON a.sap_id=b.sap_id"
                    . " LEFT JOIN sdb_sales_sap_order_items c ON a.sap_id = c.sap_id"
                    . " WHERE a.shop_id = '{$sdf['reship']['shop_id']}'"
                    . " AND a.source_bill_id = {$source_order_id} AND b.split_item_id IN (" . implode(',', $sourcePayedInfo['all_item_id']) . ")"
                    . " AND a.bill_type = '" . $this->__push_type . "'"
                    . " GROUP BY c.oriFlowNo,b.paymentCode,b.split_item_id";
                $hasPushReshipList = kernel::database()->select($sql);
                if (!empty($hasPushReshipList)) {
                    foreach ($hasPushReshipList as $row) {
                        $key = sprintf('%s_%s_%d', $row['oriFlowNo'], $row['paymentCode'], $row['split_item_id']);
                        $hasPushReshipData[$key] = $row['amount'];
                    }
                }

                # 最后一个退货单直接使用减法得到剩余的金额
                foreach ($paymentList as $k => $payment) {
                    # esb的sku金额
                    $key = sprintf('%s_%s_%d', $payment['eshopOrderSn'], $payment['paymentCode'], $payment['item_id']);
                    $paymentList[$k]['amount'] = bcsub($payment['amount'], $hasPushReshipData[$key] ?? 0, 2);
                    # 修正金额负数问题
                    if (floatval($paymentList[$k]['amount']) < 0) {
                        $error_msg = '子单号[' . $payment['eshopOrderSn'] . ']的金额不能为负数[' . $paymentList[$k]['amount'] . ']';
                        return false;
                    }
                    # 记录分摊的item_id
                    $paymentList[$k]['split_item_id'] = $payment['item_id'];
                    # 删除多余字段
                    unset($paymentList[$k]['item_id'], $paymentList[$k]['order_id']);
                }
            }

            # 获取基础物料
            $bmInfo = $this->_getBMStoreAndOrg($orderItem['bn'], true);
            if (empty($bmInfo['material_spu_id'])) {
                $error_list[] = '商品大码不能为空';
            }
            if (empty($bmInfo['org_no'])) {
                $error_list[] = '门店id不能为空';
            }
            if (empty($bmInfo['store_bn'])) {
                $error_list[] = '商户id不能为空';
            }
            if (empty($bmInfo['material_bn'])) {
                $error_list[] = '商品SKU信息不能为空';
            }

            # 扩展参数
            $extend_data = [
                'shop_type' => $shop_type,
                'order_source' => $sdf['order_source_oid'][$oid] ?? null,
            ];

            # 订单信息
            $orderHead = [
                'orderSource' => $this->getOrderSource($shop_type, $extend_data),
                'orderType' => 'REFUNDED', // 固定值
                'orderSn' => empty($sdf['return ']) ? $sdf['reship']['reship_bn'] : $sdf['return ']['return_bn'],  // 售后单号
                'eshopOrderSn' => '', // 业务订单编号
                'mallId' => $bmInfo['org_no'], // 门店Id
                'shopId' => $bmInfo['store_bn'], // 商户id
                'spuId' => $bmInfo['material_spu_id'], // 商品大码
                'productId' => $orderItem['shop_goods_id'] ?? '', // 平台商品ID
                'skuId' => $orderItem['shop_product_id'] ?? '', // 平台SKU ID
                'vipId' => $sdf['order']['card_number'] ?? '', // 会员卡号
                'orderTime' => $sdf['reship']['t_begin'] . '000', // 退单时间 长度：13位
                'orderPrice' => 0, // 订单总金额
                'discAmount' => 0, // 订单优惠金额
                'payAmount' => 0, // 订单实付金额
                'transAmount' => 0, // 运费
                'bankAmount' => 0, // 银行手续费
                'oriFlowNo' => $oid, // 流水号
            ];
            # 序号
            $indexNo = str_pad($k + 1, 3, '0', STR_PAD_LEFT);
            $orderHead['eshopOrderSn'] = $orderHead['orderSn'] . $indexNo;
            $data['orderItems'][] = $orderHead;
            # 记录oid
            $result['oid'][$oid] = $orderHead['eshopOrderSn'];
            $result['source_oid'][] = $orderItem['oid'];

            $goodsItem = [
                'eshopOrderSn' => $orderHead['eshopOrderSn'], // 在线商城业务订单编号
                'spuId' => $bmInfo['material_spu_id'], // 商品大码
                'sku' => $bmInfo['material_bn'], // 商品SKU信息
                'count' => intval($item['num']), // 订单购买商品数量
                'unitPrice' => 0, // 商品单价
                'totalPrice' => 0, // 商品总价
                'mallId' => $bmInfo['org_no'], // 门店Id
            ];
            $data['goodItems'][] = $goodsItem;

            # 其他退货单已退数量
            $key = sprintf('%d_%d', $item['order_item_id'], $item['product_id']);
            if (!empty($otherReshipItems)) {
                $otherReshipItem = $otherReshipItems[$key] ?? [];
            }
            $otherReshipNum = empty($otherReshipItem) ? 0 : $otherReshipItem['num'];
            # 订单的已发货数量
            $orderItemNum = $orderItem['nums'];

            # 总退货数量 = 本次退货数量 + 其他退货单的退货数量
            $totalReshipNum = bcadd($item['num'], $otherReshipNum);
            # 如果是全退，则无需分摊，否则要进行明细分摊
            if (bccomp($totalReshipNum, $orderItemNum) > 0) {
                $error_msg = '基础物料[' . $item['bn'] . ']当前的退货数量（' . $totalReshipNum . '）大于订单购买数量（' . $orderItemNum . '）';
                return false;
            }
            $data['paymentItems'] = array_merge($data['paymentItems'], $paymentList);
        }

        # 错误信息
        if (!empty($error_list)) {
            $error_msg = implode(';', $error_list);
            return false;
        }

        # 计算订单支付信息中的oid金额总和
        $paymentsOidList = ['payment' => [], 'discount' => []];
        # 获取支付编码
        $paymentCodeList = $this->_getPlatformPaymentCodeList($shop_type);
        # 获取优惠券code
        foreach ($data['paymentItems'] as $k => $payment) {
            $key = $result['oid'][$payment['eshopOrderSn']];
            if (empty($key)) {
                continue;
            }

            # 记录优惠券code
            if (!empty($paymentp['couponCode'])) {
                $result['coupon_id'][] = $payment['couponCode'];
            }
            # 更新子订单号
            $data['paymentItems'][$k]['eshopOrderSn'] = $key;
            # 计算订单支付明细金额之和
            $paymentsOidList['payment'][$key] = bcadd($paymentsOidList['payment'][$key], $payment['amount'], 2);
            # 计算优惠明细的总金额
            if (!in_array($payment['paymentCode'], $paymentCodeList)) {
                $paymentsOidList['discount'][$key] = bcadd($paymentsOidList['discount'][$key], $payment['amount'], 2);
            }
            # 删除扩展字段
            unset($data['paymentItems'][$k]['addon']);
        }

        # 更新订单明细中的金额
        foreach ($data['orderItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['payAmount'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['orderItems'][$k]['orderPrice'] = $data['orderItems'][$k]['payAmount'];
            }
            if (!empty($paymentsOidList['discount'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['discAmount'] = floatval($paymentsOidList['discount'][$item['eshopOrderSn']]);
            }
            # 记录订单总实付金额
            $total_pay_amount = bcadd($total_pay_amount, $data['orderItems'][$k]['payAmount'], 2);
            $total_discount_amount = bcadd($total_discount_amount, $data['orderItems'][$k]['discAmount'], 2);
        }
        # 更新sku明细中的支付金额
        foreach ($data['goodItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['goodItems'][$k]['totalPrice'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['goodItems'][$k]['unitPrice'] = floatval(bcdiv($data['goodItems'][$k]['totalPrice'], $item['count'], 2));
            }
        }

        # 获取订单优惠券信息
        $coupons = $this->_getSourceOrderCoupons($source_order_id, $shop_type, $result['coupon_id']);
        if (!empty($coupons)) {
            $data['couponItems'] = $coupons;
        }

        # 保存中间表的明细
        $this->_saveSapItemsData($data, $sdf['ext']['sap_data']);

        # 删除扩展字段
        foreach ($data['couponItems'] as $k => $item) {
            unset($data['couponItems'][$k]['eshopOrderSn']);
        }
        foreach ($data['paymentItems'] as $k => $item) {
            unset($data['paymentItems'][$k]['split_item_id']);
        }

        # 验证优惠金额是否重新计算
        if (!empty($paymentsOidList['discount']) && floatval($total_discount_amount) <= 0) {
            $error_msg = '订单信息里的优惠金额与支付金额不一致';
            return false;
        }

        # 验证订单是否推送
        $order_pushed = $this->check_push_order($source_order_id);
        if (!$order_pushed) {
            $error_msg = '原始支付订单未成功推送';
            return false;
        }

        $params = [
            'appId' => $this->__app_id,
            'timestamp' => erpapi_sap_func::mixtimestamp(true),
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'total_pay_amount' => $total_pay_amount,
        ];
        return $params;
    }

    /**
     * 退货单请求参数
     * @param $sdf
     * @param $error_msg
     * @return mixed
     */
    protected function _format_params($sdf, &$error_msg)
    {
        if (empty($sdf['reship']['reship_items'])) {
            $error_msg = '订单明细不存在';
            return false;
        }

        $shop_type = $sdf['reship']['shop_type'];
        # appid
        $this->__app_id = $this->getAppId($shop_type);
        if (empty($this->__app_id)) {
            $error_msg = '未找到店铺类型为"' . $shop_type . '"的appId映射关系';
            return false;
        }

        $error_list = $oid_mapping = [];

        $total_pay_amount = 0;
        $data = ['orderItems' => [], 'goodItems' => [], 'paymentItems' => []];
        $result = ['oid' => [], 'source_oid' => [], 'coupon_id' => [], 'return_items' => []];

        $source_order_id = $sdf['reship']['source_order_id'] ?? $sdf['reship']['order_id'];
        # 获取优惠字段
        $discount_fields = kernel::single(sprintf('erpapi_sap_mapping_platform_%s', erpapi_sap_func::getShopType($shop_type)))->getDiscountFields();
        # 数据类
        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', erpapi_sap_func::getShopType($shop_type)));
        # 获取其他退货单的已退货数量
        $otherReshipItems = $this->getOtherReshipItems($sdf['reship']['order_id'], $sdf['reship']['reship_id']);

        # 获取换货单的原始订单退入商品明细
        if (!empty($sdf['reship']['source_order_id'])) {
            $result['return_items'] = $this->_getSourceChangeItems($sdf['reship']['order_id'], $sdf['reship']['reship_items']);
        }

        foreach ($sdf['reship']['reship_items'] as $k => $item) {
            # 排除换货，如果存在换货商品，直接退出循环体
            if ($item['return_type'] == 'change') {
                break;
            }

            # 判断是否存在关联单号，如果存在，则取原始订单明细的oid
            if (!empty($sdf['reship']['source_order_id'])) {
                # 原换货单对应的退货入库sku
                $source_return_item = $result['return_items'][$item['product_id']];
                if (empty($source_return_item)) {
                    continue;
                }
                $order_item_id = $source_return_item['order_item_id'];
            } else {
                $order_item_id = $item['order_item_id'];

            }
            $sql = "select a.item_id,a.order_id,a.bn,a.name,a.nums,a.item_type,b.oid,b.history_oid,a.shop_goods_id,a.shop_product_id"
                . " from sdb_ome_order_items a LEFT JOIN sdb_ome_order_objects b ON a.order_id = b.order_id AND a.obj_id = b.obj_id"
                . " WHERE a.item_id = {$order_item_id}";
            $orderItem = kernel::database()->selectrow($sql);
            if (empty($orderItem)) {
                continue;
            }

            # 获取基础物料
            $bmInfo = $this->_getBMStoreAndOrg($orderItem['bn'], true);
            if (empty($bmInfo['material_spu_id'])) {
                $error_list[] = '商品大码不能为空';
            }
            if (empty($bmInfo['org_no'])) {
                $error_list[] = '门店id不能为空';
            }
            if (empty($bmInfo['store_bn'])) {
                $error_list[] = '商户id不能为空';
            }
            if (empty($bmInfo['material_bn'])) {
                $error_list[] = '商品SKU信息不能为空';
            }

            # 设置历史子单号
//            if (isset($sdf['ext']['is_history']) && $sdf['ext']['is_history'] == 'true') {
//                $orderItem['oid'] = empty($orderItem['history_oid']) ? $orderItem['oid'] : $orderItem['history_oid'];
//            }

            # 获取子单号
            $oid = $platformObj->getOid($orderItem['order_id'], $orderItem['oid']);

            # 扩展参数
            $extend_data = [
                'shop_type' => $shop_type,
                'order_source' => $sdf['order_source_oid'][$oid] ?? null,
            ];

            $shop_store_bn = $bmInfo['store_bn'];
            $fulfillment_store = $this->_getfulfillment_store($orderItem['order_id'],$bmInfo['material_bn']);
            if($fulfillment_store){
                $shop_store_bn = $fulfillment_store['store_bn'];
            }

            # 订单信息
            $orderHead = [
                'orderSource' => $this->getOrderSource($shop_type, $extend_data),
                'orderType' => 'REFUNDED', // 固定值
                'orderSn' => empty($sdf['return ']) ? $sdf['reship']['reship_bn'] : $sdf['return ']['return_bn'],  // 售后单号
                'eshopOrderSn' => '', // 业务订单编号
                'mallId' => $bmInfo['org_no'], // 门店Id
                'shopId' => $shop_store_bn, // 商户id
                'spuId' => $bmInfo['material_spu_id'], // 商品大码
                'productId' => $orderItem['shop_goods_id'] ?? '', // 平台商品ID
                'skuId' => $orderItem['shop_product_id'] ?? '', // 平台SKU ID
                'vipId' => $sdf['order']['card_number'] ?? '', // 会员卡号
                'orderTime' => $sdf['reship']['t_begin'] . '000', // 退单时间 长度：13位
                'orderPrice' => 0, // 订单总金额
                'discAmount' => 0, // 订单优惠金额
                'payAmount' => 0, // 订单实付金额
                'transAmount' => 0, // 运费
                'bankAmount' => 0, // 银行手续费
                'oriFlowNo' => $oid, // 流水号
            ];
            # 设置历史子单号
            /*if ($sdf['ext']['order_is_history']) {
                if (empty($orderItem['history_oid'])) {
                    $error_list[] = '子单号：' . $oid . '，对应的历史子单号为空';
                } else {
                    # 记录当前子单号对应历史子单号的映射关系，方便后续替换
                    $oid_mapping[$oid] = $orderItem['history_oid'];
                }
            }*/
            # 序号
            $indexNo = str_pad($k + 1, 3, '0', STR_PAD_LEFT);
            $orderHead['eshopOrderSn'] = $orderHead['orderSn'] . $indexNo;
            $data['orderItems'][] = $orderHead;
            # 记录oid
            $result['oid'][$oid] = $orderHead['eshopOrderSn'];
            $result['source_oid'][] = $orderItem['oid'];

            $goodsItem = [
                'eshopOrderSn' => $orderHead['eshopOrderSn'], // 在线商城业务订单编号
                'spuId' => $bmInfo['material_spu_id'], // 商品大码
                'sku' => $bmInfo['material_bn'], // 商品SKU信息
                'count' => intval($item['num']), // 订单购买商品数量
                'unitPrice' => 0, // 商品单价
                'totalPrice' => 0, // 商品总价
                'mallId' => $bmInfo['org_no'], // 门店Id
            ];
            $data['goodItems'][] = $goodsItem;

            # 其他退货单已退数量
            $key = sprintf('%d_%d', $item['order_item_id'], $item['product_id']);
            if (!empty($otherReshipItems)) {
                $otherReshipItem = $otherReshipItems[$key] ?? [];
            }
            $otherReshipNum = empty($otherReshipItem) ? 0 : $otherReshipItem['num'];
            # 订单的已发货数量
            $orderItemNum = $orderItem['nums'];
            # 获取订单支付信息
            $payments = $this->_getSouceOrderPayments($source_order_id, $oid);
            if (empty($payments)) {
                continue;
            }

            # 总退货数量 = 本次退货数量 + 其他退货单的退货数量
            $totalReshipNum = bcadd($item['num'], $otherReshipNum);
            # 如果是全退，则无需分摊，否则要进行明细分摊
            if (bccomp($totalReshipNum, $orderItemNum) > 0) {
                $error_msg = '基础物料[' . $item['bn'] . ']当前的退货数量（' . $totalReshipNum . '）大于订单购买数量（' . $orderItemNum . '）';
                return false;
            } elseif ($otherReshipNum == 0 && bccomp($item['num'], $orderItemNum) == 0) {  // 整个SKU的全部数量退货
                $data['paymentItems'] = array_merge($data['paymentItems'], $payments);
            } elseif ($otherReshipNum > 0 && bccomp($totalReshipNum, $orderItemNum) == 0) {
                // 单个SKU最后数量的退货，取剩余分摊尾款
                $splitPaymentItems = $this->_split_payment_items($payments, $otherReshipNum, $orderItemNum, true);
                if (empty($splitPaymentItems)) {
                    $error_msg = '获取支付明细失败[oid：' . $orderItem['oid'] . ']';
                    return false;
                }
                $data['paymentItems'] = array_merge($data['paymentItems'], $splitPaymentItems);
            } elseif (bccomp($totalReshipNum, $orderItemNum) < 0) {
                // 单个sku部分数量的退货，且不是最后数量的退货，需要分摊支付明细
                $splitPaymentItems = $this->_split_payment_items($payments, $item['num'], $orderItemNum);
                if (empty($splitPaymentItems)) {
                    $error_msg = '获取支付明细失败[oid：' . $orderItem['oid'] . ']';
                    return false;
                }
                $data['paymentItems'] = array_merge($data['paymentItems'], $splitPaymentItems);
            }
        }

        # 错误信息
        if (!empty($error_list)) {
            $error_msg = implode(';', $error_list);
            return false;
        }

        # 计算订单支付信息中的oid金额总和
        $paymentsOidList = ['payment' => [], 'discount' => []];
        # 获取优惠券code
        foreach ($data['paymentItems'] as $k => $payment) {
            $key = $result['oid'][$payment['eshopOrderSn']];
            if (empty($key)) {
                continue;
            }

            # 记录优惠券code
            if (!empty($paymentp['couponCode'])) {
                $result['coupon_id'][] = $payment['couponCode'];
            }
            # 更新子订单号
            $data['paymentItems'][$k]['eshopOrderSn'] = $key;
            # 计算订单支付明细金额之和
            $paymentsOidList['payment'][$key] = bcadd($paymentsOidList['payment'][$key], $payment['amount'], 2);
            # 优惠券、活动折扣、平台补贴、红包
            if (!empty($discount_fields) && in_array($payment['addon']['sub_type'], $discount_fields)) {
                $paymentsOidList['discount'][$key] = bcadd($paymentsOidList['discount'][$key], $payment['amount'], 2);
            }
            # 删除扩展字段
            unset($data['paymentItems'][$k]['addon']);
        }

        # 更新订单明细中的金额
        foreach ($data['orderItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['payAmount'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['orderItems'][$k]['orderPrice'] = $data['orderItems'][$k]['payAmount'];
            }
            if (!empty($paymentsOidList['discount'][$item['eshopOrderSn']])) {
                $data['orderItems'][$k]['discAmount'] = floatval($paymentsOidList['discount'][$item['eshopOrderSn']]);
            }
            # 记录订单总实付金额
            $total_pay_amount = bcadd($total_pay_amount, $data['orderItems'][$k]['payAmount'], 2);
        }
        # 更新sku明细中的支付金额
        foreach ($data['goodItems'] as $k => $item) {
            if (!empty($paymentsOidList['payment'][$item['eshopOrderSn']])) {
                $data['goodItems'][$k]['totalPrice'] = floatval($paymentsOidList['payment'][$item['eshopOrderSn']]);
                $data['goodItems'][$k]['unitPrice'] = floatval(bcdiv($data['goodItems'][$k]['totalPrice'], $item['count'], 2));
            }
        }

        # 获取订单优惠券信息
        $coupons = $this->_getSourceOrderCoupons($source_order_id, $shop_type, $result['coupon_id']);
        if (!empty($coupons)) {
            $data['couponItems'] = $coupons;
        }

        # 保存中间表的明细
        $this->_saveSapItemsData($data, $sdf['ext']['sap_data']);

        # 删除扩展字段
        foreach ($data['couponItems'] as $k => $item) {
            unset($data['couponItems'][$k]['eshopOrderSn']);
        }

        # 验证订单是否推送
        $order_pushed = $this->check_push_order($source_order_id);
        if (!$order_pushed) {
            $error_msg = '原始支付订单未成功推送';
            return false;
        }

        $params = [
            'appId' => $this->__app_id,
            'timestamp' => erpapi_sap_func::mixtimestamp(true),
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            //'oid_mapping' => $oid_mapping,
            'total_pay_amount' => $total_pay_amount,
        ];
        return $params;
    }

    /**
     * 保存到sap中间数据表 - 主数据
     * @param $sdf
     * @return mixed
     */
    protected function saveSapMainData($sdf, $bill_type)
    {
        $orderMdl = app::get('sales')->model('sap_orders');

        $order_data = [
            'bill_id' => $sdf['reship']['reship_id'],
            'bill_no' => $sdf['reship']['reship_bn'],
            'bill_type' => $bill_type,
            'shop_id' => $sdf['reship']['shop_id'],
            'shop_type' => $sdf['reship']['shop_type'],
            'create_time' => time()
        ];
        # 来源订单ID
        if (!empty($sdf['reship']['source_order_id'])) {
            $order_data['source_bill_id'] = implode(',', $sdf['reship']['source_order_id']);
        } else {
            $order_data['source_bill_id'] = $sdf['reship']['order_id'];
        }
        if (!empty($sdf['sap_id'])) {
            $order_data['sap_id'] = $sdf['sap_id'];
        }
        # 历史退货单无需推送
        if ($this->is_history_order($sdf['reship'])) {
            $order_data['sync_status'] = 'none';
        }
        $result = $orderMdl->save($order_data);
        if (!$result) {
            return false;
        }
        return $order_data;
    }

    protected function _check_params($params, &$error_msg)
    {
        if (isset($params['extend']) && $params['extend']['is_history']) {
            return true;
        }

        if (empty($params['orderItems'][0]['orderTime'])) {
            $error_msg = '退单时间不能为空';
            return false;
        }

        # 检查参数
        $result = parent::_check_params($params, $new_msg);
        if (!$result) {
            $error_msg = $new_msg;
            return false;
        }

        return $this->_check_refund_params($params, $error_msg);
    }

    /**
     * 获取订单其他退货明细
     * @param $order_id
     * @return mixed
     */
    private function getOtherReshipItems($order_id, $reship_id)
    {
        if (empty($order_id)) {
            return false;
        }

        $sql = "SELECT b.order_item_id,b.product_id,SUM(b.num) AS num FROM sdb_ome_reship a"
            . " LEFT JOIN sdb_ome_reship_items b ON a.reship_id = b.reship_id"
            . " WHERE a.order_id = {$order_id} AND a.is_check != '5' AND a.return_type = 'return ' AND a.reship_id != {$reship_id}"
            . " GROUP BY b.order_item_id,b.product_id";
        $result = kernel::database()->select($sql);
        if (empty($result)) {
            return false;
        }

        # 数据转换
        $data = [];
        foreach ($result as $item) {
            $key = sprintf(' % d_ % d', $item['order_item_id'], $item['product_id']);
            $data[$key] = $item;
        }
        return $data;
    }

    /**
     * 分摊支付明细
     * @param $payments
     * @param $reship_num
     * @param $total_num
     * @return mixed
     */
    private function _split_payment_items($payments, $reship_num, $total_num, $is_end = false)
    {
        if (empty($payments) || empty($reship_num) || empty($total_num)) {
            return false;
        }

        foreach ($payments as $k => $payment) {
            # 计算单价
            $price = bcdiv($payment['amount'], $total_num, 2);
            # 分摊后的金额
            $split_amount = bcmul($price, $reship_num, 2);
            # 判断是否尾款
            if ($is_end) {
                $payments[$k]['amount'] = bcsub($payment['amount'], $split_amount, 2);
            } else {
                $payments[$k]['amount'] = $split_amount;
            }
        }
        return $payments;
    }

    /**
     * 获取原始换货单对应的退入商品明细
     * @param $order_id
     * @param $reship_items
     * @return void
     */
    public function _getSourceChangeItems($order_id, $reship_items)
    {
        if (empty($order_id) || empty($reship_items)) {
            return false;
        }

        $reshipItemsMdl = app::get('ome')->model('reship_items');
        $result = [];

        foreach ($reship_items as $item) {
            if ($item['return_type'] == 'change') {
                continue;
            }

            # 基础物料ID
            $product_id = $source_product_id = $item['product_id'];
            for ($i = 0; $i < 9; $i++) {
                $sql = "SELECT DISTINCT a.reship_id,a.order_id FROM sdb_ome_reship a LEFT JOIN sdb_ome_reship_items b ON a.reship_id =b.reship_id"
                    . " WHERE a.return_type = 'change' AND a.is_check = '7' AND b.return_type ='change'"
                    . " AND b.product_id = {$product_id} AND a.change_order_id = {$order_id}";
                $changeInfo = kernel::database()->selectrow($sql);
                if (empty($changeInfo)) {
                    break;
                }

                # 获取退入商品id
                $item_filter = [
                    'reship_id' => $changeInfo['reship_id'],
                    'return_type' => 'return '
                ];
                $reshipItems = $reshipItemsMdl->getList('item_id,product_id,order_item_id', $item_filter);
                if (empty($reshipItems)) {
                    break;
                }

                $result[$source_product_id] = [
                    'reship_id' => $changeInfo['reship_id'],
                    'item_id' => $reshipItems[0]['item_id'],
                    'product_id' => $reshipItems[0]['product_id'],
                    'order_item_id' => $reshipItems[0]['order_item_id'],
                ];

                # 重新设定基础物料ID
                $product_id = $reshipItems[0]['product_id'];
                # 重新设定订单id
                $order_id = $changeInfo['order_id'];
            }
        }
        return $result;
    }
}