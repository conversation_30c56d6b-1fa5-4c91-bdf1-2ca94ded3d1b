<?php

class taskmgr_whitelist
{

    //进队列业务逻辑处理任务
    public static function task_list()
    {
        return $_tasks = array(
            'autochk' => array(
                'method' => 'wms_autotask_task_check',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 120,
            ),
            'autodly' => array(
                'method' => 'wms_autotask_task_consign',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 360,
            ),
            'autorder' => array(
                'method' => 'ome_autotask_task_combine',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 120,
                'name' => '审单队列',
            ), //自动审单
            'autoretryapi' => array(
                'method' => 'erpapi_autotask_task_retryapi',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 120,
            ),
            'autowapdly' => array('method' => 'o2o_autotask_task_statistic', 'threadNum' => 1), //wap统计发货单数据
            'ordertaking' => array(
                'method' => 'ome_autotask_task_ordertaking',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 120,
            ),
            'confirmreship' => array(
                'method' => 'ome_autotask_task_confirmreship',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 180,
            ), //售后退货单
            'confirminventory' => array(
                'method' => 'ome_autotask_task_confirminventory',
                'threadNum' => 1,
                'retry' => true,
                'timeout' => 180,
            ), //盘点确认
            // 'stocksync'     => array('method' => 'ome_autotask_task_stocksync', 'threadNum' => 5), // 库存检查
            'dailyinventory' => array(
                'method' => 'console_autotask_timer_dailyinventory',
                'threadNum' => 1,
                'timeout' => 180,
            ),
            'syncaoxiang' => array(
                'method' => 'dchain_autotask_syncproduct', //请求同步翱象商品
                'threadNum' => 1,
                'timeout' => 180,
            ),
            'mappingaoxiang' => array(
                'method' => 'dchain_autotask_mappingproduct', //请求同步翱象商品关系
                'threadNum' => 1,
                'timeout' => 180,
            ),
            'aoxiangdelivery' => array(
                'method' => 'dchain_autotask_syncdelivery', //请求同步翱象发货单
                'threadNum' => 1,
                'timeout' => 180,
            ),
            'getstore' => array(
                'method' => 'ome_autotask_task_getstore', // 同步店铺
                'threadNum' => 1,
                'timeout' => 120,
            ),
            'uniquegetmaterial' => array(
                'method' => 'material_autotask_task_uniquegetmaterial', // 库存100 - 获取商品列表
                'threadNum' => 5,
                'timeout' => 120,
            ),
            'uniquegetstock' => array(
                'method' => 'material_autotask_task_uniquegetstock', // 库存100 - 获取商品库存列表
                'threadNum' => 5,
                'timeout' => 120,
            ),
            'uniquegetwmsstock' => array(
                'method' => 'material_autotask_task_uniquegetwmsstock', // wms - 获取商品库存列表
                'threadNum' => 5,
                'timeout' => 120,
            ),
            'batchsyncstock' => array(
                'method' => 'material_autotask_task_batchsyncstock', // 批量更新基础物料的门店库存
                'threadNum' => 5,
                'timeout' => 120,
            ),
            'pushsap' => array(
                'method' => 'ome_autotask_task_pushsap', // 推送金蝶sap
                'threadNum' => 1,
                'timeout' => 120,
                'name' => '推送ESB队列'
            ),
            'sendemail' => array(
                'method' => 'material_autotask_task_sendemail', // 发送邮件
                'threadNum' => 1,
                'timeout' => 120,
            ),
            'wxshipinhistory' => array(
                'method' => 'ome_autotask_task_wxshipinhistory', // 处理微信视频号历史订单
                'threadNum' => 1,
                'timeout' => 120,
            ),
            'spuidimport' => array(
                'method' => 'ome_autotask_task_spuidimport', // 大码导入
                'threadNum' => 1,
                'timeout' => 120,
            ),
            'syncshopstock' => array(
                'method' => 'ome_autotask_task_syncshopstock', // 实时同步库存到前端店铺
                'threadNum' => 5,
                'timeout' => 120,
            ),
            'transferorderpulltask'=>array(
                'method' => 'ome_autotask_task_transferorderpull', // 拉取中转订单
                'threadNum' => 1,
                'timeout' => 120,
            ),
        );
    }

    //定时任务，线程数不允许修改
    public static function timer_list()
    {
        return $_tasks = array(
            'misctask' => array('method' => 'ome_autotask_timer_misctask', 'threadNum' => 1),
            'inventorydepth' => array('method' => 'ome_autotask_timer_inventorydepth', 'threadNum' => 1),
            'batchfill' => array('method' => 'ome_autotask_timer_batchfill', 'threadNum' => 1),
            'cleandata' => array('method' => 'ome_autotask_timer_cleandata', 'threadNum' => 1),
            'hour' => array('method' => 'ome_autotask_timer_hour', 'threadNum' => 1),
            'cancelorder' => array('method' => 'ome_autotask_timer_cancelorder', 'threadNum' => 1),
            'ordersprice' => array('method' => 'ome_autotask_timer_ordersprice', 'threadNum' => 1),
            'orderstime' => array('method' => 'ome_autotask_timer_orderstime', 'threadNum' => 1),
            'rmatype' => array('method' => 'ome_autotask_timer_rmatype', 'threadNum' => 1),
            'sale' => array('method' => 'ome_autotask_timer_sale', 'threadNum' => 1),
            'h5deliveryretry' => array('method' => 'ome_autotask_timer_h5deliveryretry', 'threadNum' => 1),
            'storestatus' => array('method' => 'ome_autotask_timer_storestatus', 'threadNum' => 1),
            'stockcost' => array('method' => 'tgstockcost_autotask_timer_stockcost', 'threadNum' => 1),
            'wms_sync_inv' => array('method' => 'console_autotask_timer_invsnapshot', 'threadNum' => 1),
            'logistestimate' => array('method' => 'ome_autotask_timer_logistestimate', 'threadNum' => 1),
            'queue' => array('method' => 'ome_autotask_timer_queue', 'threadNum' => 20, 'name' => '系统任务队列'),
            'stocknsale' => array('method' => 'ome_autotask_timer_stocknsale', 'threadNum' => 1, 'retry' => false),
            'productnsale' => array('method' => 'ome_autotask_timer_productnsale', 'threadNum' => 1, 'retry' => false),
            'storedaliy' => array('method' => 'o2o_autotask_timer_storedaliy', 'threadNum' => 1),
            'vopurchase' => array('method' => 'ome_autotask_timer_vopurchase', 'threadNum' => 1),
            'vopick' => array('method' => 'ome_autotask_timer_vopick', 'threadNum' => 1),
            'vopickinventory' => array('method' => 'ome_autotask_timer_vopickinventory', 'threadNum' => 1),
            'logisticsanalysts' => array('method' => 'ome_autotask_timer_logisticsanalysts', 'threadNum' => 1),
            // 'syncdlystatus'     => array('method' => 'ome_autotask_timer_syncdlystatus', 'threadNum' => 1),
            // 'syncwms'           => array('method' => 'erpapi_autotask_task_sync', 'threadNum' => 1),
            'financebasejob' => array('method' => 'financebase_autotask_timer_job', 'threadNum' => 1),
            'sysordertaking' => array('method' => 'ome_autotask_timer_sysordertaking', 'threadNum' => 1),
            'delaymisc' => array('method' => 'ome_autotask_timer_delaymisc', 'threadNum' => 1),
            'batchchannelmaterial' => array('method' => 'material_autotask_timer_batchchannelmaterial', 'threadNum' => 1),
            'orderdiscounts' => array('method' => 'omeanalysts_autotask_timer_orderdiscounts', 'threadNum' => 1),

            'vopbill' => array('method' => 'ome_autotask_timer_vopbill', 'threadNum' => 1),
            'vopreturn' => array('method' => 'ome_autotask_timer_vopreturn', 'threadNum' => 1),
            'aoxiangsync' => array('method' => 'dchain_autotask_aoxiangsync', 'threadNum' => 1), //翱象同步商品
            'aoxiangmapping' => array('method' => 'dchain_autotask_aoxiangmapping', 'threadNum' => 1), //翱象同步商品关系
            'dailyinvmonitor' => array('method' => 'console_autotask_timer_dailyinvmonitor', 'threadNum' => 1), // 库存核对报警通知
            'sendnotify' => array('method' => 'monitor_autotask_timer_sendnotify', 'threadNum' => 1),
            'invoice_makeoutinvoice' => array('method' => 'invoice_autotask_timer_makeoutinvoice', 'threadNum' => 1),
            'invoice_redapply_sync' => array('method' => 'invoice_autotask_timer_redapply_sync', 'threadNum' => 1),

            # 每天上午9点、晚上21点自动拉取门店信息
            'sapgetshoplist' => array('method' => 'ome_autotask_timer_sapgetshoplist', 'threadNum' => 1),
            //生成拣货单 暂定早上8点
            'genpick' => array('method' => 'o2o_autotask_timer_genpick', 'threadNum' => 1),
            //检查非7天无理由指定渠道客服超24小时未审
            'twentyfourcheck' => array('method' => 'o2o_autotask_timer_twentyfourcheck', 'threadNum' => 1),
            //部分退款和库存不足订单自动审核
            'autoauditbyunmyown' => array('method' => 'ome_autotask_timer_autoauditbyunmyown', 'threadNum' => 1),
            // 自动拦截全额退款已发货订单
            'fullrefundorderinterception' => array('method' => 'ome_autotask_timer_fullrefundorderinterception', 'threadNum' => 1),
            'interceptionretry' => array('method' => 'ome_autotask_timer_interceptionretry', 'threadNum' => 1),
            // 库存同步预警 - 每天早 9、下午 17:00 同步两次
            'stocksyncwarning' => array('method' => 'ome_autotask_timer_stocksyncwarning', 'threadNum' => 1),
            // 同步平台地址库 - 每天早 9、下午 21:00 同步两次
            'syncshopreturnaddr' => array('method' => 'ome_autotask_timer_syncshopreturnaddr', 'threadNum' => 1),
            //未发货预警 - 每天 9点和15点
            'orderdeliverywarning' => array('method' => 'ome_autotask_timer_orderdeliverywarning', 'threadNum' => 1),
            //未发货仅退款预警 - 每天 9点和15点
            'orderrefundwarning' => array('method' => 'ome_autotask_timer_orderrefundwarning', 'threadNum' => 1),
            //物流拦截失败邮件预警 - 2小时一次
            'interceptionretrywarning' => array('method' => 'ome_autotask_timer_interceptionretrywarning', 'threadNum' => 1),
            //已退款未入库邮件预警 - 每小时
            'refundednostockwarning' => array('method' => 'ome_autotask_timer_refundednostockwarning', 'threadNum' => 1),
            //已入库未退款邮件预警 - 每小时
            'storednorefundwarning' => array('method' => 'ome_autotask_timer_storednorefundwarning', 'threadNum' => 1),
            //发货状态回传平台失败邮件预警 - 每隔一小时
            'orderconsignewarning' => array('method' => 'ome_autotask_timer_orderconsignewarning', 'threadNum' => 1),
            // 定时检查订单是否推送esb - 每小时
            'ordertoesbcheck' => array('method' => 'ome_autotask_timer_ordertoesbcheck', 'threadNum' => 1),
            // 物流状态为已揽收超过14天后自动改成为已签收（从确认发货时间开始算）
            'deliverysigncheck' => array('method' => 'ome_autotask_timer_deliverysigncheck', 'threadNum' => 1),
            //失败订单预警 上午9:00，中午13:00，下午16:00 渠道（店铺名称） 订单号 支付时间 错误商品编码
            'orderreceivefail' => array('method' => 'ome_autotask_timer_orderreceivefail', 'threadNum' => 1),
            // 商品同步失败预警（每隔5分钟重试3次）
            'retrysyncskuwarning' => array('method' => 'ome_autotask_timer_retrysyncskuwarning', 'threadNum' => 1),
            // 商品库存同步失败（每隔5分钟重试3次）
            'retrysyncstockwarning' => array('method' => 'ome_autotask_timer_retrysyncstockwarning', 'threadNum' => 1),
            //开票失败定时任务
            'invoicefailwarning'=>array('method' => 'invoice_autotask_timer_failwarning', 'threadNum' => 1),
            // 未发货超过48小时订单微信通知
            'deliverytimeout' => array('method' => 'monitor_autotask_timer_deliverytimeout', 'threadNum' => 1),
            // 发送微信消息，3分钟执行一次
            'sendweixinnotify' => array('method' => 'monitor_autotask_timer_sendweixinnotify', 'threadNum' => 1),
            // 买家申请退换货，10分钟执行一次
            'orderrefundweixin' => array('method' => 'monitor_autotask_timer_orderrefund', 'threadNum' => 1),
           //补录订单号订阅
            'insertexpresshqepay'=> array('method' => 'ome_autotask_timer_insertexpresshqepay', 'threadNum' => 1),
            //库存编码更改
            // 'storebnupdate'=> array('method' => 'ome_autotask_timer_storebnupdate', 'threadNum' => 1),
            //门店没有开启接单，或者没有配置接单时间预警
            'storereceivewarning'=> array('method' => 'o2o_autotask_timer_storereceivewarning', 'threadNum' => 1),
            //拉中转订单
            'transferorderpull'=> array('method' => 'ome_autotask_timer_transferorderpull', 'threadNum' => 1),

        );
    }

    //初始化域名进任务队列,这里的命名规范就是实际连的队列任务+domainqueue生成这个初始化任务的数组值，线程数不允许修改
    public static function init_list()
    {
        return $_tasks = array(
            'misctaskdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '*/30 * * * * *',
            ),
            'inventorydepthdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */5 * * * *',
            ),
            'batchfilldomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */30 * * * *',
            ),
            'cleandatadomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 1 * * *',
            ),
            'hourdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 * * * *',
            ),
            'cancelorderdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */10 * * * *',
            ),
            'orderspricedomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 1 * * *',
            ),
            'orderstimedomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 1 * * *',
            ),
            'rmatypedomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 2 * * *',
            ),
            'saledomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 2 * * *',
            ),
            'h5deliveryretrydomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */5 * * * *',
            ),
            // 'catsalestatisdomainqueue'     => array(
            //     'threadNum' => 1,
            //     'rule' => '0 0 3 * * *',
            // ),
            // 'productsalerankdomainqueue'   => array(
            //     'threadNum' => 1,
            //     'rule' => '0 0 3 * * *',
            // ),
            'storestatusdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 4 * * *',
            ),
            'stockcostdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 1 * * *',//OMS库存快照生成时间要早于wms_sync_invdomainqueue获取WMS物料库存执行时间
            ),
            'wms_sync_invdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 2 * * *',
            ),
            'logistestimatedomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 5 * * *',
            ),
            'stocknsaledomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 1 * * *',
            ),
            'productnsaledomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 2 * * *',
            ),
            'storedaliydomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 3 * * *',
            ),
            'vopurchasedomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 * * * *',
            ),
            'vopickdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */5 * * * *',
            ),
            'vopickinventorydomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */5 * * * *',
            ),
            'logisticsanalystsdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 3 * * *',
            ),
            'financebasejobdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 * * * * *',
            ),
            'sysordertakingdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */3 * * * *',
            ),
            'delaymiscdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '*/10 * * * * *',
            ),
            'batchchannelmaterialdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */10 * * * *',
            ),
            'orderdiscountsdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */30 * * * *',
            ),
            'vopbilldomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */30 * * * *',
            ),
            'vopreturndomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */30 * * * *',
            ),
            'aoxiangsyncdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 * * * *',
            ),
            'aoxiangmappingdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 * * * *',
            ),
            'dailyinvmonitordomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 8 * * *',
            ),
            'sendnotifydomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */5 * * * *',
            ),
            'sendweixinnotifydomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */3 * * * *',
            ),
            'invoice_makeoutinvoicedomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */10 * * * *',
            ),
            'invoice_redapply_sync' => array(
                'threadNum' => 1,
                'rule' => '0 */30 * * * * ',
            ),
            'sapgetshoplist' => array(
                'threadNum' => 1,
                'rule' => '01 1 * * *',
            ),
            'genpickdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 8 * * *',
            ),
            'twentyfourcheckdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '*/30 * * * *',
            ),
            'autoauditbyunmyowndomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */5 * * * *',
            ),
            'fullrefundorderinterceptiondomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */3 * * * *',
            ),
            'interceptionretrydomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */3 * * * *',
            ),
            'stocksyncwarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 * * * *',
            ),
            'syncshopreturnaddrdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,21 * * *',
            ),
            'orderdeliverywarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,14,20 * * *',
            ),
            'orderrefundwarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,15 * * *',
            ),
            'interceptionretrywarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,14,20 * * *',
                'retry' => false,
            ),
            'refundednostockwarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,14,20 * * *',
            ),
            'storednorefundwarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,14,20 * * *',
            ),
            'orderconsignewarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 * * * *',
            ),
            'ordertoesbcheckdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 0 * * * *',
            ),
            'deliverysigncheckdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9 * * *',
            ),
            'orderreceivefaildomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,13,16 * * *',
            ),
            'retrysyncskuwarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,17 * * *',
            ),
            'retrysyncstockwarningdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 00 9,17 * * *',
            ),
            'invoicefailwarningdomainqueue'=>array(
                'threadNum' => 1,
                'rule' => '0 00 9,15 * * *',
            ),
            'deliverytimeoutdomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */10 * * * *',
            ),
            'orderrefundweixindomainqueue' => array(
                'threadNum' => 1,
                'rule' => '0 */10 * * * *',
            ),
            'insertexpresshqepaydomainqueue'=>array(
                'threadNum' => 1,
                'rule' => '0 0 */2 * * *',
                'retry' => false,
            ),
            // 'storebnupdatedomainqueue'=>array(
            //     'threadNum' => 1,
            //     'rule' => '0 */5 * * * *',
            // ),
            'storereceivewarningdomainqueue'=>array(
                'threadNum' => 1,
                'rule' => '0 0 10 * * *',
                'retry' => false,
            ),
            'transferorderpulldomainqueue'=>array(
                'threadNum' => 1,
                'rule' => '0 */30 * * * *',
                'retry' => false,
            ),

        );
    }

    //导出任务
    public static function export_list()
    {
        return $_tasks = array(
            'exportsplit' => array(
                'method' => 'ome_autotask_export_exportsplit',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 180,
                'name' => '导出数据分片队列',
            ),
            'dataquerybysheet' => array(
                'method' => 'ome_autotask_export_dataquerybysheet',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 600,
                'name' => '分页导出队列',
            ),
            'dataquerybyquicksheet' => array(
                'method' => 'ome_autotask_export_dataquerybyquicksheet',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 600,
                'name' => '快速导出队列',
            ),
            'dataquerybywhole' => array(
                'method' => 'ome_autotask_export_dataquerybywhole',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 3600,
                'name' => '完整导出队列',
            ),
            'createfile' => array(
                'method' => 'ome_autotask_export_createfile',
                'threadNum' => 5,
                'retry' => true,
                'timeout' => 3600,
                'name' => '导出文件生成队列',
            ),
        );
    }

    //rpc任务
    public static function rpc_list()
    {
        return $_tasks = array(
            'omecallback' => array('method' => 'ome_autotask_rpc_omecallback', 'threadNum' => 5),
            'wmscallback' => array('method' => 'ome_autotask_rpc_wmscallback', 'threadNum' => 5),
            'wmsrpc' => array(
                'method' => 'ome_autotask_rpc_wmsrpc',
                'threadNum' => 5,
                'name' => 'WMS发货通知队列',
            ),
            'orderrpc' => array(
                'method' => 'ome_autotask_rpc_orderrpc',
                'threadNum' => 5,
                'name' => '收单队列',
            ),
        );
    }

    public static function finance_list()
    {
        return $_tasks = array(
            'billapidownload' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //财务下载任务
            'billassign' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //账单导入分派任务
            'cainiaoassignorder' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //菜鸟导入分派任务
            'cainiaoassignsku' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //菜鸟导入分派任务
            'cainiaoassignsale' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //账单导入分派任务
            'billimport' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //账单导入
            'cainiaoorderimport' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //菜鸟根据订单导入
            'cainiaoskuimport' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //菜鸟根据sku导入
            'cainiaosaleimport' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //菜鸟根据销售周期导入
            'syncaftersales' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //同步售后单
            'syncsales' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //同步销售单
            'verificationassign' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //分派流水单核销
            'verificationprocess' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //账单核算自动对账任务
            'initmonthlyreport' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //生成新账期任务
            'expensessplit' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //费用拆分
            'cainiaoassignjzt' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //京准通导入分派任务
            'cainiaojztimport' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //京准通导入保存数据任务
            'cainiaoassignjdbill' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //京东钱包流水导入分派任务
            'cainiaojdbillimport' => array('method' => 'financebase_autotask_task_process', 'threadNum' => 1), //京东钱包流水导入保存数据任务

            'assign' => array('method' => 'omecsv_autotask_task_process', 'threadNum' => 1), //分片导入任务
            'import' => array('method' => 'omecsv_autotask_task_process', 'threadNum' => 1), //分片导入

        );
    }

    //全部任务
    public static function get_all_task_list()
    {
        return array_merge(self::task_list(), self::timer_list(), self::export_list(), self::rpc_list(), self::finance_list());
    }

    public static function get_task_types()
    {
        return array('task', 'timer', 'init', 'export', 'rpc', 'finance');
    }
}
