            <tr>
                <th>订单缓冲时间：</th>
                <td><select name="set[auto.setting][bufferTime]" >
                    <option value="1" <{if $setData.auto_setting.bufferTime == '1'}>selected<{/if}>>1分钟</option>
                    <option value="2" <{if $setData.auto_setting.bufferTime == '2'}>selected<{/if}>>2分钟</option>
                    <option value="3" <{if $setData.auto_setting.bufferTime == '3'}>selected<{/if}>>3分钟</option>
                    <option value="4" <{if $setData.auto_setting.bufferTime == '4'}>selected<{/if}>>4分钟</option>
                    <option value="5" <{if $setData.auto_setting.bufferTime == '5'}>selected<{/if}>>5分钟</option>
                    <option value="10" <{if $setData.auto_setting.bufferTime == '10'}>selected<{/if}>>10分钟</option>
                    <option value="30" <{if $setData.auto_setting.bufferTime == '30'}>selected<{/if}>>30分钟</option>
                    <option value="60" <{if $setData.auto_setting.bufferTime == '60'}>selected<{/if}>>1小时</option>
                    <option value="120" <{if $setData.auto_setting.bufferTime == '120'}>selected<{/if}>>2小时</option>
                    <option value="180" <{if $setData.auto_setting.bufferTime == '180'}>selected<{/if}>>3小时</option>
                    <option value="240" <{if $setData.auto_setting.bufferTime == '240'}>selected<{/if}>>4小时</option>
                </select><{help}>用户下单后需等待一定的时间后才进入处理流程，能有效解决多个订单合并的问题<{/help}></td>
            </tr>