<?php

/**
 * 拦截失败重试
 */
class ome_autotask_timer_interceptionretry
{

    public function process($params, &$error_msg = '')
    {
        set_time_limit(0);
        ignore_user_abort(1);
        @ini_set('memory_limit', '512M');
        $interceptionObj = app::get('ome')->model('interception');
        $operationLogObj =app::get('ome')->model('operation_log');
        $intLib = kernel::single('ome_interception');
        //获取失败次数小于5的拦截订单
        $list = $interceptionObj->getList('*',['retry_count|lthan'=>ome_interception::$max_retry_count,'status'=>['failed','running']]);

        $exit_retry_count = [2,6,8];

        foreach ($list as $row){
            $msg = '';
            $interceptionObj->update(['retry_count'=>($row['retry_count']+1),'email_status'=>0],['id'=>$row['id']]);
            $result = $intLib->interception_express($row['return_id'],$msg);
            if($msg!='' || $result!=true){
                //拦截失败 2,6,8 次取消运单
                $cur_count = $row['retry_count']+1;

                if(in_array($cur_count,$exit_retry_count)){
                    $cancelRes = $intLib->cancel_express($row['return_id'],$msg);
                    if(!$cancelRes){
                        $operationLogObj->write_log('interception_update@ome', $row['id'], '发货单快递取消失败,取消运单失败');
                    }else {
                        $operationLogObj->write_log('interception_update@ome', $row['id'], '发货单快递取消失败,取消运单成功');
                    }
                }

                //超过10次发邮件
                if($cur_count >= ome_interception::$max_retry_count){
                    $intLib->sendEmail($row);
                }
            }
        }
        return true;
    }
}
