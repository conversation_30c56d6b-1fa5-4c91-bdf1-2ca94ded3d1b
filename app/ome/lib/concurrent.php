<?php
/**
 * 防止并发导致数据插入失败
 * @copyright Copyright (c) 2011, shopex. inc
 * <AUTHOR>
 * 
 */

class ome_concurrent{
    
	 /**
     * 自动清除同步日志
     * 每天检测将超2天的日志数据清除
     */
    public function clean(){
        
        $now = strtotime(date("Y-m-d"));
        $db = kernel::database();

        $where = " WHERE `current_time`<'".($now-2*24*60*60)."' ";
        $del_sql = " DELETE FROM `sdb_ome_concurrent` $where ";
        $db->exec($del_sql);
        
        $del_sql = 'DELETE FROM `sdb_ome_concurrent` WHERE `current_time` IS NULL';
        $db->exec($del_sql);

        $del_sql = 'OPTIMIZE TABLE `sdb_ome_concurrent`';
        $db->exec($del_sql);
    }

    /**
     * 删除指定时间之前
     * @param $id
     * @param $type
     * @param $time
     */
    public function deleteTimeBefor($id, $type, $time)
    {
        $db = kernel::database();
        $del_sql = "DELETE FROM `sdb_ome_concurrent` WHERE id='{$id}' and type='{$type}' and `current_time`<{$time}";
        $db->exec($del_sql);
    }
}