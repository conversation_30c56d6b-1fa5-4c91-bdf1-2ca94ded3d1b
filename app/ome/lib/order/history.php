<?php
class ome_order_history
{

    // 处理退款订单
    public function processRefund($sdf)
    {
        kernel::database()->beginTransaction();
        try {
            $history_obj_id = array_column($sdf['items'], 'history_obj_id');
            $order_objects = app::get('ome')->model('order_objects')->dump(['history_obj_id' => $history_obj_id], '*');
            if (empty($order_objects)) {
                throw new Exception('未找到历史子订单');
            }

            $orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $order_objects['order_id']), '*');
            if ($sdf['status'] == '已退款') {
                $status = '4';
            } elseif ($sdf['status'] == '退款中') {
                // todo单拉售后申请单
            } else {
                throw new Exception('退款单状态错误:' . $sdf['status']);
            }

            $obj_ids = $order_objects['obj_id'];
            $order_items = app::get('ome')->model('order_items')->dump(['order_id' => $order_objects['order_id'], 'obj_id' => $obj_ids], '*');
            if (empty($order_items)) {
                throw new Exception('未找到订单商品');
            }

            // 退款金额不能大于子单金额
            $refund_fee = bcmul(bcdiv($order_objects['divide_order_fee'], $order_items['quantity'], 3), $sdf['items'][0]['num'], 2);
            if ($sdf['refund_fee'] > $refund_fee) {
                $sdf['refund_fee'] = $refund_fee;
            }

            // 通过其中一个商品判断是否已发货
            if ($order_items['sendnum'] == 0) {
                $refund_refer = '0';
            } else {
                $refund_refer = '1';
            }

            if ($sdf['status'] == '退款中') {
                // 退货中的需要补单
                #$returnRsp = kernel::single('erpapi_router_request')->set('shop', $orderInfo['shop_id'])->finance_getRefundDetail($sdf['refund_bn'], '', $orderInfo['order_bn']);
                #if ($returnRsp['rsp'] == 'succ') {
                #    $msg = '';
                #    kernel::single('ome_return')->get_return_log($returnRsp['data'], $orderInfo['shop_id'], $msg);
                #}
                #kernel::database()->commit();
                return true;
            }

            if ($sdf['is_delivery']) {
                $aftersale_result = $this->processAftersale($orderInfo, $sdf, $order_objects, $order_items);
            }

            $oids = $order_objects['oid'];
            $esb_data = $this->_esb_order_amount($order_objects['order_id'], $orderInfo['shop_type'], $oids);
            //data
            $data = array(
                'order_id' => $order_objects['order_id'],
                'refund_apply_bn' => $sdf['refund_bn'],
                'pay_type' => 'online',
                'account' => $sdf['account'],
                'money' => $sdf['refund_fee'],
                'refunded' => $sdf['refund_fee'],
                'memo' => '历史退款单',
                'create_time' => $sdf['refund_time'],
                'status' => $status,
                'shop_id' => $orderInfo['shop_id'],
                'addon' => serialize(array('refund_bn' => $sdf['refund_bn'])),
                'source' => 'matrix',
                'shop_type' => $orderInfo['shop_type'],
                'outer_lastmodify' => $sdf['refund_time'],
                'refund_refer' => $refund_refer, //退款来源
                'org_id' => $orderInfo['org_id'],
                'bn' => $order_items['bn'],
                'oid' => $oids, //oid子单
                'bool_type' => $sdf['bool_type'],
                'is_history' => 'true',
                'esb_amount' => $esb_data['esb_amount'],
                'esb_pmt_amount' => $esb_data['esb_pmt_amount'],
            );

            if ($aftersale_result['return_id']) {
                $data['return_id'] = $aftersale_result['return_id'];
            }

            if ($aftersale_result['reship_id']) {
                $data['reship_id'] = $aftersale_result['reship_id'];
            }

            $arrProduct[] = array(
                'product_id' => $order_items['product_id'],
                'bn' => $order_items['bn'],
                'name' => $order_items['name'],
                'num' => $sdf['items'][0]['num'], // 需要使用传入的退款数量
                'price' => $sdf['items'][0]['price'], // 需要改成传入的退款金额
                'order_item_id' => $order_items['item_id'],
                'oid' => $oids,
                'modified' => time(),
            );
            $data['product_data'] = serialize($arrProduct);
            //根据商品编码获取门店信息
            $store = kernel::single("o2o_store_material")->getOrderStore($order_items['bn'],$order_objects['order_id']);
            if ($store) {
                $belong_type = $store['performance_type'] == 'store' ? 'store' : 'town';
                $store_id = $store['store_id'];
            } else {
                $belong_type = 'customer';
                $store_id = 0;
            }
            $data['belong_store_id'] = $store_id;
            $data['belong_type'] = $belong_type;

            $refund_apply_info = app::get('ome')->model('refund_apply')->dump(['refund_apply_bn' => $sdf['refund_bn']], 'apply_id');
            if ($refund_apply_info) {
                app::get('ome')->model('refund_apply')->update($data, ['refund_apply_bn' => $sdf['refund_bn']]);
            } else {
                app::get('ome')->model('refund_apply')->insert($data);
            }
            if ($status == '4') {
                $refundData = array(
                    'refund_bn' => $sdf['refund_bn'],
                    'shop_id' => $orderInfo['shop_id'],
                    'order_id' => $order_objects['order_id'],
                    'currency' => 'CNY',
                    'money' => $sdf['refund_fee'],
                    'cur_money' => $sdf['refund_fee'],
                    'pay_type' => 'online',
                    'download_time' => $sdf['refund_time'],
                    'status' => 'succ',
                    'memo' => '历史退款单',
                    'trade_no' => $sdf['refund_bn'],
                    'modified' => $sdf['refund_time'],
                    't_ready' => $sdf['refund_time'],
                    't_sent' => $sdf['refund_time'],
                    't_received' => $sdf['refund_time'],
                    'org_id' => $orderInfo['org_id'],
                    'refund_refer' => $refund_refer, //退款来源
                    'is_history' => 'true',
                );

                //存在退款申请单且存在门店ID和所属类型
                if ($data['belong_store_id'] && $data['belong_type']) {
                    $refundData['belong_store_id'] = $data['belong_store_id'];
                    $refundData['belong_store_bn'] = $data['belong_store_bn'];
                    $refundData['belong_type'] = $data['belong_type'];
                }

                $refunds = app::get('ome')->model('refunds')->dump(['refund_bn' => $sdf['refund_bn']], 'refund_id');
                if ($refunds) {
                    app::get('ome')->model('refunds')->update($refundData, ['refund_bn' => $sdf['refund_bn']]);
                } else {
                    app::get('ome')->model('refunds')->insert($refundData);
                }

                if ($aftersale_result['reship_id']) {
                    $reship_sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_reship_sdf($aftersale_result['reship_id']);
                    kernel::single('erpapi_router_request')->set('sap', true)->reship_push($reship_sdf);
                } else {
                    $refund_apply_info = app::get('ome')->model('refund_apply')->dump(['refund_apply_bn' => $sdf['refund_bn']], 'apply_id');
                    $refund_sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_refund_sdf($refund_apply_info['apply_id']);
                    kernel::single('erpapi_router_request')->set('sap', true)->refund_push($refund_sdf);
                }
            }

            $sql ="update sdb_ome_orders set payed=IF((CAST(payed AS char)-IFNULL(0,cost_payment)-".$sdf['refund_fee'].")>=0,payed-IFNULL(0,cost_payment)-".$sdf['refund_fee'].",0)  where order_id=".$order_objects['order_id'];
            kernel::database()->exec($sql);

            kernel::single('ome_order_func')->update_order_pay_status($order_objects['order_id'],true);

            kernel::database()->commit();
        } catch (Exception $e) {
            kernel::database()->rollBack();
            $msg = $e->getMessage();
            $this->refundlog($msg, $sdf['refund_bn']);
            echo $msg . "," . $sdf['refund_bn'] . "\n";
            return false;
        }
        return true;
    }

    private function _esb_order_amount($order_id, $shop_type, $oids)
    {
        $result = ['esb_amount' => 0, 'esb_pmt_amount' => 0];
        if (empty($order_id) || empty($oids)) {
            return $result;
        }

        # 数据类
        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', $shop_type));
        # 判断订单是否为换货单，换货单不推送sap
        $is_change = app::get('ome')->model('reship')->is_change_order($order_id);
        # 如果是换货单，esb的订单金额设置为0，其他普通订单则读取金额
        if ($is_change) {
            return $result;
        }

        $oids = is_array($oids) ? $oids : explode(',', $oids);
        foreach ($oids as $oid) {
            # 子单号
            $esb_oid = $platformObj->getOid($order_id, $oid);
            # 根据oid查询推送esb明细的订单金额
            $esb_order_item = $platformObj->getSourceOrderItems($order_id, $esb_oid);
            $esb_amount = empty($esb_order_item) ? 0 : $esb_order_item['payAmount'];
            $esb_pmt_amount = empty($esb_order_item) ? 0 : $esb_order_item['discAmount'];

            $result['esb_amount'] = bcadd($result['esb_amount'], $esb_amount, 2);
            $result['esb_pmt_amount'] = bcadd($result['esb_pmt_amount'], $esb_pmt_amount, 2);
        }
        return $result;
    }


    // 处理售后订单
    public function processAftersale($orderInfo, $sdf, $order_objects, $order_items)
    {
        $modelReturnProduct = app::get('ome')->model('return_product');

        $opInfo = kernel::single('ome_func')->get_system();
        $data = array(
            'return_bn' => $sdf['refund_bn'],
            'shop_id' => $orderInfo['shop_id'],
            'member_id' => $orderInfo['member_id'],
            'order_id' => $orderInfo['order_id'],
            'title' => $orderInfo['order_bn'] . '售后申请单',
            'content' => '历史售后单',
            'comment' => '历史售后单',
            'add_time' => $sdf['refund_time'],
            'status' => '4',
            'op_id' => $opInfo['op_id'],
            'refundmoney' => $sdf['refund_fee'],
            'money' => $sdf['refund_fee'],
            'shipping_type' => 'EXPRESS',
            'source' => 'matrix',
            'shop_type' => $orderInfo['shop_type'],
            'outer_lastmodify' => $sdf['refund_time'],
            'delivery_id' => $sdf['delivery_id'],
            'memo' => '',
            'org_id' => $orderInfo['org_id'],
            'flag_type' => '0',
            'platform_status' => 'SUCCESS', //平台售后单状态
            'apply_remark' => '', //售后申请描述
            'kinds' => 'reship',
            'jsrefund_flag' => '0', //极速退款标识
            'is_history' => 'true',
            'shop_type' => $orderInfo['shop_type'],
        );

        //售后类型
        $data['return_type'] = 'return';

        //平台订单号
        $data['platform_order_bn'] = $orderInfo['order_bn'];

        //获取发货门店信息
        $storeInfo = kernel::single("o2o_store_material")->getBmDelivStore($order_items['bn'], $data['order_id']);
        $sdf['branch_id'] = $storeInfo['branch_id'];
        $data['branch_id'] = $sdf['branch_id'];
        if (empty($sdf['branch_id'])) {
            throw new Exception('未找到发货门店信息');
        }
        $data['is_fail'] = 'false';
        //退货单增加赠品明细
        $data['return_gift_items'] = app::get('ome')->model('reship')->addReturnGiftItems($data['return_product_items'], $data['order_id'], $sdf['branch_id']);
        $data['belong_store_id'] = isset($storeInfo['store_id']) ? $storeInfo['store_id'] : 0;
        $data['belong_store_bn'] = isset($storeInfo['store_bn']) ? $storeInfo['store_bn'] : '';
        if ($storeInfo) {
            $data['belong_type'] = $storeInfo['performance_type'] == 'store' ? 'store' : 'town';
        } else {
            $data['belong_type'] = 'customer';
        }

        $data['return_product_items'][] = array(
            'product_id' => $order_items['product_id'] ? $order_items['product_id'] : 0,
            'bn' => $order_items['bn'],
            'name' => $order_items['title'] ? $order_items['title'] : $order_items['name'],
            'num' => $sdf['items'][0]['num'],
            'price' => $sdf['items'][0]['price'],
            'amount' => $sdf['items'][0]['amount'],
            'branch_id' => $sdf['branch_id'],
            'order_item_id' => $order_items['item_id'],
            'shop_goods_bn' => $order_objects['shop_goods_id'],
            'obj_type' => $order_objects['obj_type'],
            'quantity' => $sdf['items'][0]['num'],
        );

        $returnProductItems = $data['return_product_items'];

        //获取商品org_id
        if ($data['return_product_items']) {
            $bn = $data['return_product_items'][0]['bn'];
            $data['org_id'] = kernel::single('o2o_store_material')->getOrgByMaterialBn($bn);
        }

        $return_info = $modelReturnProduct->dump(['return_bn' => $sdf['refund_bn']], 'return_id');
        if ($return_info) {
            $modelReturnProduct->update($data, ['return_id' => $return_info['return_id']]);
            $data['return_id'] = $return_info['return_id'];
        } else {
            $modelReturnProduct->insert($data);
            $data['return_id'] = $data['return_id'];
            foreach ($returnProductItems as &$val) {
                $val['return_id'] = $data['return_id'];
            }
            $modelItem = app::get('ome')->model('return_product_items');
            $sql = ome_func::get_insert_sql($modelItem, $returnProductItems);
            $modelItem->db->exec($sql);
        }

        // 如果是已退款状态，自动创建退换货单并完成入库
        if ($sdf['status'] == '已退款') {
            $reship_id = $this->createReship($data, $storeInfo, $returnProductItems);
        }

        return ['reship_id' => $reship_id, 'return_id' => $data['return_id']];
    }

    // 生成退换货单，并且自动完成
    public function createReship($data, $storeInfo, $returnProductItems)
    {
        $modelReship = app::get('ome')->model('reship');

        // 设置退换货单基本信息
        $reshipData = array(
            'reship_bn' => $data['return_bn'],
            'order_id' => $data['order_id'],
            'platform_order_bn' => $data['platform_order_bn'],
            'shop_id' => $data['shop_id'],
            'member_id' => $data['member_id'],
            'return_type' => $data['return_type'],
            'problem_id' => $data['problem_id'] ? $data['problem_id'] : 0,
            'status' => 'succ', // 自动完成
            'is_check' => '7',
            'return_reason' => $data['return_reason'] ? $data['return_reason'] : '历史退换货单',
            'check_time' => $data['add_time'],
            'modified_time' => $data['add_time'],
            'org_id' => $data['org_id'],
            'return_id' => $data['return_id'],
            'is_history' => 'true',
            'return_type' => 'return',
            'branch_id' => $data['branch_id'],
            'outer_lastmodify' => $data['add_time'],
            'shop_type' => $data['shop_type'],
            'refund_status' => 'finish',
            'logi_status' => '1',
            'embrace_time' => $data['add_time'],
            'wait_customer_check' => '2',
            't_begin' => $data['add_time'],
            't_end' => $data['add_time'],
        );

        if ($storeInfo) {
            $reshipData['belong_type'] = $storeInfo['performance_type'] == 'store' ? 'store' : 'town';
            $reshipData['belong_store_id'] = $storeInfo['store_id'];
            $reshipData['belong_store_bn'] = $storeInfo['store_bn'];
        } else {
            $reshipData['belong_type'] = 'customer';
            $reshipData['belong_store_id'] = 0;
        }

        $reshipInfo = $modelReship->dump(['reship_bn' => $data['return_bn']], 'reship_id');
        if ($reshipInfo) {
            $modelReship->update($reshipData, ['reship_id' => $reshipInfo['reship_id']]);
            $reship_id = $reshipInfo['reship_id'];
        } else {
            $modelReship->insert($reshipData);
            $reship_id = $reshipData['reship_id'];

            // 处理退换货商品明细
            if ($reship_id && !empty($returnProductItems)) {
                $reshipItems = array();
                foreach ($returnProductItems as $item) {
                    $reshipItems[] = array(
                        'reship_id' => $reship_id,
                        'product_id' => $item['product_id'],
                        'bn' => $item['bn'],
                        'name' => $item['name'],
                        'num' => $item['num'],
                        'normal_num' => $item['num'], // 设置良品数量等于总数量，用于入库
                        'price' => $item['price'],
                        'amount' => $item['amount'],
                        'order_item_id' => $item['order_item_id'],
                        'shop_goods_bn' => $item['shop_goods_bn'],
                        'obj_type' => $item['obj_type'],
                        'quantity' => $item['quantity'],
                        'return_type' => 'return' // 设置退货类型为return
                    );
                    kernel::database()->exec('UPDATE sdb_ome_order_items SET return_num=return_num+'.$item['quantity'].' WHERE order_id='.$data['order_id'].' AND bn=\''.$item['bn'].'\' AND item_id='.$item['order_item_id']);
                }
                // 批量插入退换货商品明细
                $modelReshipItem = app::get('ome')->model('reship_items');
                $sql = ome_func::get_insert_sql($modelReshipItem, $reshipItems);
                $modelReshipItem->db->exec($sql);
            }

            // 自动完成入库
            if ($reship_id) {
                // 记录操作日志
                $operationLogObj = app::get('ome')->model('operation_log');
                $operationLogObj->write_log('reship@ome', $reship_id, '历史退换货单自动入库');

                $reshipObj = app::get('ome')->model('reship');
                $reshipObj->finish_aftersale($reship_id);

                // 调用入库方法
                kernel::single('console_reship')->siso_iostockReship($reship_id);
            }
        }

        return $reship_id;
    }

    /**
     * 创建历史订单
     * @param string $shop_id 店铺ID
     * @param string $order_bn 订单编号
     * @param array $deliveryList 发货单列表
     * @param array $orderDataItems 订单商品数据
     * @return bool
     */
    public function processOrder($shop_id, $order_bn, $deliveryList, $orderDataItems, $is_refund = false)
    {
        kernel::database()->beginTransaction();
        try {
            // 参数验证
            if (empty($shop_id) || empty($order_bn)) {
                throw new Exception('店铺ID和订单编号不能为空');
            }

            $orderInfo = app::get('ome')->model('orders')->dump(['order_bn' => $order_bn], 'order_id,shop_id,shop_type');
            if (empty($orderInfo)) {
                // 创建订单
                $res = $this->getOrder($shop_id, $order_bn, $deliveryList, $orderDataItems);
                if (!$res) {
                    throw new Exception('创建订单失败');
                }
            } else {
                kernel::database()->commit();
                return true;
            }

            // 获取订单ID
            $orderInfo = app::get('ome')->model('orders')->dump(['order_bn' => $order_bn], 'order_id,shop_id,shop_type');
            if (!$orderInfo) {
                throw new Exception('创建订单失败:未找到订单信息');
            }

            $order_id = $orderInfo['order_id'];
            $sdf = kernel::single('ome_sap_data_platform_' . $orderInfo['shop_type'])->get_order_sdf($order_id);
            kernel::single('erpapi_router_request')->set('sap', true)->order_push($sdf);

            // 处理发货单
            if (!empty($deliveryList)) {
                foreach ($deliveryList as $delivery) {
                    $this->processDelivery($order_id, $delivery);
                }
            }

            if ($is_refund) {
                app::get('ome')->model('orders')->update(['pay_status' => '6'], ['order_id' => $order_id]);
            }

            if ($orderDataItems) {
                foreach ($orderDataItems as $row) {
                    if ($row['ship_status'] == '未发货' && $row['pay_status'] == '已退款') {
                        $order_objects = app::get('ome')->model('order_objects');
                        $objectInfo = $order_objects->dump(['order_id' => $order_id, 'history_obj_id' => $row['history_obj_id']], 'obj_id');
                        app::get('ome')->model('order_objects')->update(['delete' => 'true'], ['obj_id' => $objectInfo['obj_id'], 'order_id' => $order_id]);
                        app::get('ome')->model('order_items')->update(['delete' => 'true'], ['order_id' => $order_id, 'obj_id' => $objectInfo['obj_id']]);
                    }
                }
            }

            kernel::database()->commit();

            foreach ($deliveryList as $delivery) {
                $logi_no = $delivery['logi_no'];
                $wapDeliveryBill = app::get('wap')->model('delivery_bill')->getList('*', array('logi_no' => $logi_no), '*');
                $wap_delivery_ids = array_column($wapDeliveryBill, 'delivery_id');
                $wapDeliveryInfo = app::get('wap')->model('delivery')->dump(array('order_bn' => $order_bn, 'delivery_id'=>$wap_delivery_ids), '*');
                if ($wapDeliveryInfo) {

                    $omsDeliveryInfo = app::get('ome')->model('delivery')->dump(array('delivery_bn'=>$wapDeliveryInfo['delivery_bn']));

                    $deliveryItems = $this->getDeliveryItems($order_id, $delivery);
                    foreach ($deliveryItems['delivery_items'] as $deliveryItem) {
                        $filter = array(
                            'delivery_id' => $wapDeliveryInfo['delivery_id'],
                            'bn' => $deliveryItem['bn'],
                        );

                        $updateData = array(
                            'esb_amount' => $deliveryItem['esb_amount'],
                            'esb_pmt_amount' => $deliveryItem['esb_pmt_amount'],
                        );
                        app::get('wap')->model('delivery_items')->update($updateData, $filter);

                        // 更新oms发货单的金额
                        $omsfilter = array(
                            'delivery_id' => $omsDeliveryInfo['delivery_id'],
                            'bn' => $deliveryItem['bn'],
                        );
                        app::get('ome')->model('delivery_items')->update($updateData, $omsfilter);
                    }
                }
            }

            $order_items = app::get('ome')->model('order_items')->getList('bn,product_id', ['order_id' => $order_id]);
            foreach($order_items as $row) {
                $product_id = $row['product_id'];
                kernel::single('ome_sync_product')->reset_freeze($product_id);
                //重置预占流水记录
                kernel::single('console_stock_freeze')->reset_stock_freeze($product_id);
            }

            return true;
        } catch (Exception $e) {
            kernel::database()->rollBack();
            // 记录错误日志
            error_log(sprintf(
                "[%s] 创建历史订单失败: %s, order_bn=%s\n",
                date('Y-m-d H:i:s'),
                $e->getMessage(),
                $order_bn
            ), 3, 'history_order_error.log');

            echo $e->getMessage();
            return false;
        }
    }

    /**
     * 获取并创建订单
     * @param string $shop_id 店铺ID
     * @param string $order_bn 订单编号
     * @param array $deliveryList 发货单列表
     * @param array $orderDataItems 订单商品数据
     * @return bool
     */
    public function getOrder($shop_id, $order_bn, $deliveryList, $orderDataItems)
    {
        // 处理发货商品
        $delivery_items = $this->processDeliveryItems($deliveryList);

        // 获取订单详情
        $obj_syncorder = kernel::single("ome_syncorder");
        $return_data = kernel::single('erpapi_router_request')->set('shop', $shop_id)->order_get_order_detial($order_bn);
        #$file = DATA_DIR . '/../script/update/script/prd/order.json';
        #$return_data = json_decode(file_get_contents($file), true);

        if (!in_array($return_data['rsp'], ['success', 'succ'])) {
            throw new Exception('获取订单详情失败');
        }

        // 构建订单数据
        $sdf_order = $this->buildOrderData($return_data['data']['trade'], $delivery_items, $orderDataItems);

        // 创建订单
        $msg = '';
        if (!$obj_syncorder->get_order_log($sdf_order, $shop_id, $msg)) {
            throw new Exception('创建订单失败: ' . $msg);
        }

        // 更新历史子订单
        $this->updateHistoryOrderObjects($sdf_order, $orderDataItems);

        // 记录操作日志
        $this->writeOperationLog($order_bn);

        return true;
    }

    /**
     * 处理发货商品数据
     * @param array $deliveryList
     * @return array
     */
    private function processDeliveryItems($deliveryList)
    {
        $delivery_items = [];
        if (!empty($deliveryList)) {
            foreach ($deliveryList as $logi_no => $delivery) {
                foreach ($delivery['items'] as $sku_id => $item) {
                    $delivery_items[$sku_id] = $item;
                }
            }
        }
        return $delivery_items;
    }

    /**
     * 构建订单数据
     * @param array $trade 订单交易数据
     * @param array $delivery_items 发货商品数据
     * @return array
     */
    private function buildOrderData($trade, $delivery_items, $orderDataItems)
    {
        $sdf_order = $trade;
        $sdf_order['ship_status'] = 'SHIP_NO';
        $sdf_order['pay_status'] = 'PAY_FINISH';
        $sdf_order['status'] = 'TRADE_ACTIVE';
        $sdf_order['is_history'] = 'true';

        // 处理历史物料替换为最新基础物料
        $sdf_order = $this->updateHistoryMaterial($sdf_order, $delivery_items, $orderDataItems);

        // 处理部分退款的时候，优惠金额是负数的情况
        $sdf_order = $this->getOrderItems($sdf_order, $orderDataItems);

        return $sdf_order;
    }

    // 处理部分退款的时候，优惠金额是负数的情况
    private function getOrderItems($sdf_order, $orderDataItems)
    {
        foreach($sdf_order['orders']['order'] as &$row){
            foreach($row['order_items']['orderitem'] as &$item){
                if ($item['part_mjz_discount'] < 0) {
                    $kdAmount = $orderDataItems[$item['sku_id']]['kdAmount'];
                    $item['sale_price'] = $row['sale_price'];
                    $item['total_item_fee'] = bcmul($item['price'], $item['num'], 2);
                    $ipart_mjz_discount = bcsub($row['sale_price'], $item['divide_order_fee'], 2);
                    $item['part_mjz_discount'] = $ipart_mjz_discount;
                    $row['part_mjz_discount'] = $ipart_mjz_discount;
                }
            }
        }

        return $sdf_order;
    }

    /**
     * 更新历史子订单
     * @param array $sdf_order 订单数据
     * @param array $delivery_items 发货商品数据
     */
    private function updateHistoryOrderObjects($sdf_order, $orderDataItems)
    {
        $order_bn = $sdf_order['order_bn'] ? $sdf_order['order_bn'] : $sdf_order['tid'];
        $orderInfo = app::get('ome')->model('orders')->dump(['order_bn' => $order_bn], 'order_id');

        if (!$orderInfo) {
            throw new Exception('订单不存在' . $order_bn);
        }

        $order_objects = app::get('ome')->model('order_objects');
        foreach ($orderDataItems as $row) {
            $order_object = $order_objects->dump(['order_id' => $orderInfo['order_id'], 'oid' => $row['sku_id']], 'obj_id');
            if ($order_object) {
                $order_objects->update(['history_oid' => $row['history_oid'], 'history_obj_id' => $row['history_obj_id']], ['obj_id' => $order_object['obj_id'], 'order_id' => $orderInfo['order_id']]);
            }
        }
        return true;
    }

    /**
     * 记录操作日志
     * @param string $order_bn 订单编号
     */
    private function writeOperationLog($order_bn)
    {
        $orderInfo = app::get('ome')->model('orders')->dump(['order_bn' => $order_bn], 'order_id');

        if ($orderInfo) {
            $logObj = app::get('ome')->model('operation_log');
            $logObj->write_log('order_create@ome', $orderInfo['order_id'], '历史订单导入成功');
        }
    }

    // 处理历史物料替换为最新基础物料
    function updateHistoryMaterial($sdf_order, $delivery_items, $orderDataItems)
    {
        foreach ($sdf_order['orders']['order'] as &$row) {

            if ($row['bn'] && strlen($row['bn']) < 6) {
                $row['bn'] = '';
            }

            // 验证货号是否一致
            if ($row['bn']) {
                $store_bn = $orderDataItems[$row['oid']]['customerStoreCode'];
                if (empty($store_bn)) {
                    throw new Exception('导入参数错误，无店铺');
                }

                $material_model = app::get('material')->model('basic_material');
                $material_info = $material_model->dump(['material_bn' => $row['bn']], 'bm_id,store_id');
                if ($material_info) {
                    $storeInfo = app::get('o2o')->model('store')->db_dump(array('store_id'=>$material_info['store_id']),'store_bn');
                    if ($storeInfo['store_bn'] != $store_bn) {
                        $row['bn'] = '';
                    }
                }
            }

            if (empty($row['bn'])) {
                if ($orderDataItems[$row['oid']]) {
                    $row['bn'] =  $row['iid'] . '-' . $row['oid'];
                    $row['order_items']['orderitem'][0]['bn'] = $row['bn'];
                } else {
                    throw new Exception('物料编号不能为空');
                }
            }

            // 判断销售物料是否存在，如果不存在则新增销售物料和基础物料
            if ($delivery_items[$row['oid']]) {

                $row['order_items']['orderitem'][0]['status'] = 'active';

                $row['is_history'] = 'true';
                $material_bn = $this->createMaterial($row, $delivery_items[$row['oid']]);
                $material_info = app::get('material')->model('basic_material')->dump(['material_bn' => $material_bn], 'bm_id');
                // 更新关联表的图片
                app::get('material')->model('basic_material_ext')->update(['banner' => $delivery_items[$row['oid']]['image']], ['bm_id' => $material_info['bm_id']]);
            } elseif ($orderDataItems[$row['oid']]) {
                $row['order_items']['orderitem'][0]['status'] = 'active';
                $row['is_history'] = 'true';
                $material_bn = $this->createMaterial($row, $orderDataItems[$row['oid']]);
                $material_info = app::get('material')->model('basic_material')->dump(['material_bn' => $material_bn], 'bm_id');
                // 更新关联表的图片
                app::get('material')->model('basic_material_ext')->update(['banner' => $orderDataItems[$row['oid']]['image']], ['bm_id' => $material_info['bm_id']]);
            } else {
                $row['is_history'] = 'false';
            }
        }
        return $sdf_order;
    }

    function createMaterial($objects, $item)
    {
        $order_items = $objects['order_items']['orderitem'][0];
        $color = '';
        $size = '';
        foreach ($order_items['sku_properties'] as $sku_properties) {
            if ($sku_properties['label'] == '颜色') {
                $color = $sku_properties['value'];
            }

            if ($sku_properties['label'] == '尺码') {
                $size = $sku_properties['value'];
            }
        }

        #拼接数据
        $master = [
            'material_bn' => $objects['bn'],
            'material_spu' => $objects['bn'],
            'cost' => $objects['price'],
            'retail_price' => $objects['sale_price'],
            'spu_id' => $objects['iid'],
            'material_name' => $objects['title'],
            'store_bn' => $item['customerStoreCode'],
            'status' => '1',  // 商品状态：0-下架，1-上架
            'material_spu_id' => $item['largeSize'],  // 商品大码
            'busness_material_bn' => $item['itemNo'], // 商户货号
            'updatetime' => time(),
            'color' => $item['color'] ? $item['color'] : $color,
            'size' => $item['size'] ? $item['size'] : $size,
            'category1_bn' => 'history01',
            'category2_bn' => 'history02',
            'category3_bn' => 'history03',
            'category1_name' => '历史物料',
            'category2_name' => '历史物料',
            'category3_name' => '历史物料',
        ];

        $material_model = app::get('material')->model('basic_material');
        $material_info = $material_model->dump(['material_bn' => $master['material_bn']], 'bm_id');
        if ($material_info) {
            return $master['material_bn'];
        }

        $result['data'] = [$master];
        $params = array();
        $error_msg = '';
        kernel::single('material_kucun100')->updateMaterial($params, $result, $error_msg);

        $this->updateMaterialToHistory($master['material_bn']);

        return $master['material_bn'];
    }

    /**
     * 将基础物料更新为历史商品
     *
     * @param string $material_bn 物料编号
     * @return boolean 更新结果
     */
    public function updateMaterialToHistory($material_bn)
    {
        if (empty($material_bn)) {
            return false;
        }

        $material_model = app::get('material')->model('basic_material');
        $material_info = $material_model->dump(['material_bn' => $material_bn], 'bm_id');

        if (empty($material_info)) {
            throw new Exception('物料不存在' . $material_bn);
            return false;
        }

        $update_data = [
            'is_history' => 1,
            'updatetime' => time()
        ];

        return $material_model->update($update_data, ['material_bn' => $material_bn]);
    }

    public function processDelivery($order_id, $deliveryItems)
    {
        $sql = "SELECT dord.delivery_id,d.delivery_bn FROM sdb_ome_delivery_order AS dord
                        LEFT JOIN sdb_ome_delivery AS d ON(dord.delivery_id=d.delivery_id)
                        WHERE dord.order_id=$order_id AND (d.parent_id=0 OR d.is_bind='true') AND d.disabled='false' AND d.status IN('ready','progress')";
        $deliveryData = kernel::database()->selectrow($sql);
        if ($deliveryData) {
            // 如果有未发货的发货单，直接撤销重新生成
            app::get('ome')->model('orders')->rebackDeliveryByOrderId($order_id, false, '历史订单重新导入撤销');
        }

        $delivery = $this->getDeliveryItems($order_id, $deliveryItems);
        // 创建发货单
        $res = $this->addDelivery($order_id, $delivery);
        if ($res['rsp'] == 'succ') {
            $delivery_bn = $res['data'];
        } else {
            throw new Exception('创建发货单失败:' . $res['msg']);
        }

        // 模拟发货
        $msg = '';
        $rs = $this->simulateDelivery($delivery_bn, $deliveryItems, $msg);
        if (!$rs) {
            throw new Exception('模拟发货失败' . $msg);
        }
        return true;
    }

    // 模拟发货
    public function simulateDelivery($delivery_bn, $deliveryItems, &$msg)
    {
        $wapDeliveryInfo = app::get('wap')->model('delivery')->dump(array('delivery_bn' => $delivery_bn), '*');

        // 模拟发货
        $wap_delivery_id = $wapDeliveryInfo['delivery_id'];
        $logi_no = $deliveryItems['logi_no'];
        $logi_id = $deliveryItems['logi_id'];
        $logi_code = $deliveryItems['logi_code']; // 物流公司编码
        $logi_name = $deliveryItems['logi_company']; // 物流公司名称

        $msg = '';
        // 补录快递单号
        $data = array('delivery_id' => $wap_delivery_id, 'logi_no' => $logi_no, 'logi_code' => $logi_code, 'is_history' => '1');
        $res = kernel::single('wap_delivery_process')->insertExpress($data);
        if ($res['rsp'] == 'succ') {

            #wap发货单更新
            $dlydata = array();
            $delivery_time = $deliveryItems['shipTime'];

            $dlydata['status'] = 3;
            $dlydata['process_status'] = 7;
            $dlydata['logi_status'] = '3';
            $dlydata['last_modified'] = $delivery_time;
            $dlydata['delivery_time'] = $delivery_time;
            app::get('wap')->model('delivery')->update($dlydata, ['delivery_id' => $wap_delivery_id]);


            $orderObj    = app::get('ome')->model('orders');

            $dlyObj = app::get('ome')->model('delivery');
            $deliveryInfo = $dlyObj->dump(array('delivery_bn' => $delivery_bn), '*', array('delivery_items' => array('*'), 'delivery_order' => array('*')));

            $de     = $deliveryInfo['delivery_order'];
            $or     = array_shift($de);
            $ord_id = $or['order_id'];

            $updateSdf = array();
            $updateSdf['logi_no'] = $logi_no;
            $updateSdf['logi_id'] = $logi_id;
            $orderObj->update($updateSdf, array('order_id' => $ord_id));

            //订单发货数量更新
            $dlyObj->consignOrderItem($deliveryInfo);

            $singledly['logi_id'] = $logi_id;
            $singledly['logi_name'] = $logi_name;
            $singledly['logi_no'] = $logi_no;
            // 更新主发货单
            $singledly['delivery_id']          = $deliveryInfo['delivery_id'];
            $singledly['delivery_bn']          = $delivery_bn;
            $singledly['process']              = 'true';
            $singledly['status']               = 'succ';
            $singledly['logi_status']          = '3';
            $singledly['delivery_time']        = $delivery_time;

            //打印状态
            $singledly['expre_status'] = 'true';
            $singledly['deliv_status'] = 'true';
            $singledly['stock_status'] = 'true';


            $affect_row = $dlyObj->update($singledly, array('delivery_id' => $singledly['delivery_id'], 'process' => 'false'));
            if (!is_numeric($affect_row) || $affect_row <= 0) {
                throw new Exception('发货单发货状态更新失败!');
            }
            $operationLogObj = app::get('ome')->model('operation_log');
            $operationLogObj->write_log('delivery_process@ome', $deliveryInfo['delivery_id'], '发货单发货完成,（发货单号：' . $delivery_bn . '）', '');

            $item_num = $dlyObj->countOrderSendNumber($ord_id);
            if ($item_num == 0) {
                $orderInfo = $orderObj->db_dump(['order_id' => $ord_id]);
                // 检测京东订单是否有微信支付先用后付的单据
                $use_before_payed = false;
                if (($deliveryInfo['is_cod'] == 'false' && !$use_before_payed) || ($deliveryInfo['is_cod'] == 'false' && $use_before_payed && $orderInfo['pay_status'] == '1') || ($deliveryInfo['is_cod'] == 'true' && $orderInfo['pay_status'] == '1')) {
                    $orderdata['status'] = 'finish';
                }
                $orderdata['archive']     = 1; //订单归档
                $orderdata['ship_status'] = '1';
                $orderdata['delivery_time'] = $delivery_time;
                $affect_order             = $orderObj->update($orderdata, array('order_id' => $ord_id)); //更新订单发货状态
            } else {
                //部分发货
                $orderdata['ship_status'] = '2';
                $orderdata['delivery_time'] = $delivery_time;
                $affect_order             = $orderObj->update($orderdata, array('order_id' => $ord_id)); //更新订单发货状态
            }

            if (!is_numeric($affect_order) || $affect_order <= 0) {
                throw new Exception('订单状态更新失败!');
            }

            //标记当前门店履约订单已发货
            kernel::single('ome_o2o_performance_orders')->updateProcessStatus($ord_id, 'consign');

            $wdMdl = app::get('console')->model('wms_delivery');
            $wdRow = $wdMdl->db_dump(['delivery_id' => $deliveryInfo['delivery_id'], 'delivery_status' => '2'], 'id');
            if ($wdRow) {
                $wdRs = $wdMdl->update(['delivery_status' => '3'], ['id' => $wdRow['id'], 'delivery_status' => '2']);
                if (!is_bool($wdRs)) {
                    app::get('ome')->model('operation_log')->write_log('wms_delivery@console', $wdRow['id'], '历史发货单完成');
                }
            }

            $sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_delivery_sdf($deliveryInfo['delivery_id']);
            kernel::single('erpapi_router_request')->set('sap', true)->delivery_push($sdf);
        } else {
            $msg = $res['msg'] ? $res['msg'] : 'H5模拟发货失败!';
            throw new Exception($msg);
        }
        return true;
    }

    private function getDeliveryItems($order_id, $deliveryItems)
    {
        $order = app::get('ome')->model('orders')->dump(['order_id' => $order_id], '*', array('order_objects' => array('*', array('order_items' => array('*')))));

        $platformObj = kernel::single(sprintf('ome_sap_data_platform_%s', $order['shop_type']));

        $delivery = [];
        foreach ($order['order_objects'] as $obj) {

            if (!$deliveryItems['items'][$obj['oid']]) {
                continue;
            }

            foreach ($obj['order_items'] as $item) {
                # 过滤已删除的明细
                if ($item['delete'] == 'true') {
                    continue;
                }

                # 子单号
                $esb_oid = $platformObj->getOid($order['order_id'], $obj['oid']);
                # 根据oid查询推送esb明细的订单金额
                $esb_order_item = $platformObj->getSourceOrderItems($order['order_id'], $esb_oid);
                $esb_amount = empty($esb_order_item) ? 0 : $esb_order_item['payAmount'];
                $esb_pmt_amount = empty($esb_order_item) ? 0 : $esb_order_item['discAmount'];

                if ($delivery['delivery_items'][$item['product_id']]) {
                    $delivery['delivery_items'][$item['product_id']]['number'] += $deliveryItems['items'][$obj['oid']]['num'];
                    # 累计ESB商家收入、ESB优惠金额
                    $delivery['delivery_items'][$item['product_id']]['esb_amount'] += $esb_amount;
                    $delivery['delivery_items'][$item['product_id']]['esb_pmt_amount'] += $esb_pmt_amount;
                } else {
                    $delivery['delivery_items'][$item['product_id']] = array(
                        'item_type' => $item['item_type'],
                        'product_id' => $item['product_id'],
                        'shop_product_id' => $item['shop_product_id'],
                        'bn' => $item['bn'],
                        'number' => $deliveryItems['items'][$obj['oid']]['num'],
                        'product_name' => $item['name'],
                        'spec_info' => $item['addon'],
                        'esb_amount' => $esb_amount,         // ESB商家收入
                        'esb_pmt_amount' => $esb_pmt_amount, // ESB优惠金额
                    );
                }

                $storeInfo = app::get('o2o')->model('store')->dump(array("store_id" => $item['fulfillment_store_id']), "branch_id,name,store_bn");
                $delivery['branch_id'] = $storeInfo['branch_id'];
                $delivery['consignee'] = $order['consignee'];
                $delivery['logi_id'] = $deliveryItems['logi_id'];

                $delivery['order_items'][] = array(
                    'item_id' => $item['item_id'],
                    'product_id' => $item['product_id'],
                    'number' => $deliveryItems['items'][$obj['oid']]['num'],
                    'bn' => $item['bn'],
                    'product_name' => $item['name'],
                    'oid' => $obj['oid'],
                    's_type' => $obj['s_type'],
                    'esb_amount' => $esb_amount,         // ESB商家收入
                    'esb_pmt_amount' => $esb_pmt_amount, // ESB优惠金额
                );
            }
        }
        return $delivery;
    }

    /*
     * 新建发货单
     *
     * @param bigint $order_id 订单id
     * @param array $ship_info 收货人相关信息
     * @param $split_status  拆单后订单状态
     * @param $is_diff_order 补差价订单生成已发货的发货单 不涉及库存
     * @return $int $delivery_id 发货单id
     */
    public function addDelivery($order_id, $delivery)
    {
        $order_items = $delivery['order_items'];

        $oOrder    = app::get("ome")->model("orders");
        $oDly_corp = app::get("ome")->model("dly_corp");

        // 验证明细
        if (!$delivery['delivery_items']) {
            throw new Exception('没有明细');
        }

        //防止订单编辑与生成发货单并发导致错误
        $oOrder->update(['last_modified' => time()], ['order_id' => $order_id]);
        $psRow = app::get('ome')->model('order_platformsplit')->db_dump(['order_id' => $order_id], 'id');
        if ($psRow) {
            throw new Exception('已经进行京东平台拆，不能生成发货单');
        }
        $order = $oOrder->dump($order_id);

        $ship_info           = $delivery['consignee'] ? $delivery['consignee'] : $order['consignee'];
        $delivery_bn         = $delivery['delivery_bn'] ? $delivery['delivery_bn'] : app::get('ome')->model('delivery')->gen_id();
        $data['delivery_bn'] = $delivery_bn;
        $is_protect          = $delivery['is_protect'] ? $delivery['is_protect'] : $order['shipping']['is_protect'];

        $is_cod = $delivery['is_cod'] ? $delivery['is_cod'] : $order['shipping']['is_cod'];
        if ($is_cod) {
            $data['is_cod'] = $is_cod;
        }
        if ($order['order_type'] == 'vopczc') {
            $delivery['type'] = 'vopczc';
        }
        $data['delivery']       = $delivery['delivery'] ? $delivery['delivery'] : $order['shipping']['shipping_name'];
        $data['logi_id']        = $delivery['logi_id'];
        $data['memo']           = $delivery['memo'];
        $data['delivery_group'] = $delivery['delivery_group'];
        $data['sms_group']      = $delivery['sms_group'];
        $data['branch_id']      = $delivery['branch_id'];
        $data['wms_channel_id'] = $delivery['wms_channel_id'] ?: kernel::single('console_delivery_yjdf')->getWMSChannelId($delivery['branch_id'], $delivery['delivery_items']); //WMS渠道ID

        //平台订单号
        if (kernel::single('ome_order_bool_type')->isJDLVMI($order['order_bool_type'])) {
            $data['platform_order_bn'] =   $order['platform_order_bn'];
        } elseif ($order['platform_order_bn']) {
            $data['platform_order_bn'] = $order['platform_order_bn'];
        }

        $logi_name = "";
        if ($delivery['type']) {
            $data['type'] = $delivery['type'];
        }

        //计算预计物流费用
        $weight = 0;
        if (isset($delivery['weight'])) {
            $weight = $delivery['weight'];
        } else {
            //[拆单]根据发货单中货品详细读取重量
            $orderSplitLib = kernel::single('ome_order_split');
            $split_seting  = $orderSplitLib->get_delivery_seting();

            if ($split_seting) {
                $weight = $orderSplitLib->getDeliveryWeight($order_id, $order_items);
            } else {
                $weight = app::get('ome')->model('orders')->getOrderWeight($order_id);
            }
        }

        list($area_prefix, $area_chs, $area_id) = explode(':', $ship_info['area']);

        $price = 0.00;
        if ($delivery['logi_id']) {
            $price     = app::get('ome')->model('delivery')->getDeliveryFreight($area_id, $delivery['logi_id'], $weight);
            $dly_corp  = $oDly_corp->dump($delivery['logi_id']);
            $logi_name = $dly_corp['name'];
            //计算保价费用
            $protect = $dly_corp['protect'];
            if ($protect == 'true') {
                $is_protect    = 'true';
                $protect_price = $dly_corp['protect_rate'] * $weight;
                $cost_protect  = max($protect_price, $dly_corp['minprice']);
            }
        }

        //[同城配]配送方式
        if ($dly_corp['corp_model'] == 'instatnt') {
            //同城配送
            $data['delivery'] = 'instatnt';
        } elseif ($dly_corp['corp_model'] == 'seller') {
            //商家配送
            $data['delivery'] = 'seller';
        }

        //order has logi_info：aikucun
        if ($delivery['delivery_waybillCode']) {
            $data['logi_no'] = $delivery['delivery_waybillCode'];
        }

        if ($delivery['delivery_sub_waybillCode']) {
            $data['logi_number'] = count($delivery['delivery_sub_waybillCode']) + 1;
        }

        $data['logi_name']            = $logi_name;
        $data['is_protect']           = $is_protect ? $is_protect : 'false';
        $data['create_time']          = time();
        $data['cost_protect']         = $cost_protect ? $cost_protect : '0';
        $data['net_weight']           = $weight;
        $data['delivery_cost_expect'] = $price;
        $data['member_id']            = $delivery['member_id'] ? $delivery['member_id'] : $order['member_id'];
        $data['shop_id']              = $order['shop_id'];
        $data['shop_type']            = $order['shop_type'];
        $data['org_id']               = $order['org_id'];

        $data['delivery_items'] = $delivery['delivery_items'];
        $data['consignee']      = $ship_info;

        //支持四、五级地区
        $temp_area                     = explode('/', $area_chs);
        $data['consignee']['province'] = $temp_area[0];
        $data['consignee']['city']     = $temp_area[1];
        $data['consignee']['district'] = $temp_area[2];
        $data['consignee']['town']     = empty($temp_area[3]) ? '' : $temp_area[3];
        $data['consignee']['village']  = empty($temp_area[4]) ? '' : $temp_area[4];

        $data['order_createtime'] = ($order['paytime'] && $is_cod == 'false') ? $order['paytime'] : $order['createtime']; #付款时间为空时取创建时间

        $opInfo          = kernel::single('ome_func')->getDesktopUser();
        $data['op_id']   = $opInfo['op_id'];
        $data['op_name'] = $opInfo['op_name'];

        //得物急速现货
        if ($order['order_type'] == 'jisuxianhuo') {
            $data['bool_type'] = $data['bool_type'] | ome_delivery_bool_type::__JISU_CODE;
        }

        if ($order['order_type'] == 'platform') {
            $data['original_delivery_bn'] = $delivery_bn;
            $data['bool_type'] = ($data['bool_type'] ? $data['bool_type'] : 0) | ome_delivery_bool_type::__PLATFORM_CODE;
        }
        if ($order['shop_type'] == '360buy') {
            if (kernel::single('ome_bill_label_shsm')->isTinyPieces($order['order_id'])) {
                $data['bool_type'] = ($data['bool_type'] ? $data['bool_type'] : 0) | ome_delivery_bool_type::__SHSM_CODE;
            }
        }
        $bns      = array();
        $totalNum = 0;
        foreach ($data['delivery_items'] as $v) {
            $totalNum += $v['number'];
            $bns[$v['product_id']] = $v['bn'];
        }
        ksort($bns);

        //11.25新增
        $data['skuNum']     = count($delivery['delivery_items']);
        $data['itemNum']    = $totalNum;
        $data['bnsContent'] = serialize($bns);
        $data['idx_split']  = $data['skuNum'] * 10000000000 + sprintf("%u", crc32($data['bnsContent']));

        $data['bind_key'] = app::get('ome')->model('delivery')->getBindKey($data);
        $data['bool_type']  = (int)$data['bool_type'];
        $data['is_history'] = 'true';

        //save
        $result = app::get('ome')->model('delivery')->save($data);

        if (!$result || !$data['delivery_id']) {
            throw new Exception('发货单生成失败');
        }

        if ($delivery['type'] != 'reject') {
            //库存管控
            $storeManageLib = kernel::single('ome_store_manage');
            $storeManageLib->loadBranch(array('branch_id' => $delivery['branch_id']));

            $params = array();
            $params['params']    = array_merge($delivery, array('order_id' => $order_id, 'shop_id' => $order['shop_id'], 'delivery_id' => $data['delivery_id'], 'order_type' => $order['order_type']));
            $params['node_type'] = 'addDly';

            if (!kernel::single('ome_order_object_splitnum')->addDeliverySplitNum($order_items)) {
                throw new Exception('明细已经生成发货单');
            }

            // 判断是否已经拆分完
            $is_splited   = app::get('ome')->model('order_items')->is_splited($order_id);
            $split_status = $is_splited ? 'splited' : 'splitting';
            app::get('ome')->model('orders')->update(['process_status' => $split_status], ['order_id' => $order_id]);

            //有优惠明细记录，实付进行重算
            $order_items = app::get('ome')->model('delivery')->_regroupDeliveryItemDetailData($order_id, $order_items);
        }

        if ($data['delivery_id'] && !empty($order_items) && is_array($order_items)) {
            app::get('ome')->model('delivery')->create_delivery_items_detail($data['delivery_id'], $order_items);
        }

        //插关联表
        if ($order_id) {
            $rs  = kernel::database()->exec('SELECT * FROM sdb_ome_delivery_order WHERE 0=1');
            $ins = array('order_id' => $order_id, 'delivery_id' => $data['delivery_id']);
            $sql = kernel::single("base_db_tools")->getinsertsql($rs, $ins);
            kernel::database()->exec($sql);
        }

        //更新订单相应状态
        app::get('ome')->model('delivery')->updateOrderLogi($data['delivery_id'], $data);

        //标签写入发货单
        kernel::single('ome_bill_label')->orderToDeliveryLabel($order_id, $data['delivery_id']);

        //标记当前门店履约订单已分派
        $branchLib = kernel::single('ome_branch');
        $store_id = $branchLib->isStoreBranch($delivery['branch_id']);
        if ($store_id) {
            kernel::single('ome_o2o_performance_orders')->updateProcessStatus($order_id, 'confirm');
        }

        ome_delivery_notice::create($data['delivery_id']);

        return array('rsp' => 'succ', 'data' => $data['delivery_bn']);
    }

    private function refundlog($msg, $refund_bn)
    {
        error_log($refund_bn . ',' . $msg . "\n", 3, DATA_DIR.'/history_refund_error.log');
    }
}
