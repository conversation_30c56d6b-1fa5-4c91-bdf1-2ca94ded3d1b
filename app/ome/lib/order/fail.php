<?php
class ome_order_fail{

    private $_obj_alias = array(
        'goods'       => '商品',
        'pkg'         => '促销类',
        'gift'        => '赠品',
        'giftpackage' => '礼包',
        'lkb' => '福袋类',
        'pko' => '多选一',
    );

    //批量修复失败订单
    public function batchModifyOrder(&$cursor_id,$params){
        //danny_freeze_stock_log
        define('FRST_TRIGGER_OBJECT_TYPE','订单：失败订单恢复批量修改');
        define('FRST_TRIGGER_ACTION_TYPE','ome_order_fail：batchModifyOrder');
        $oldPbn = $params['sdfdata']['oldPbn'];
        $pbn = $params['sdfdata']['pbn'];
        $opinfo = $params['opinfo'];
        foreach($params['sdfdata']['orderId'] as $val){
            $this->addFailOrderLog($val,$opinfo);//失败订单操作日志记录添加
            $this->modifyOrderItemsByBn($val,$oldPbn,$pbn, $params['sdfdata']['modifyType']);
        }
        return false;
    }

    public function modifyOrder($order_id){
        $orderObj = app::get('ome')->model('orders');
        $order = $orderObj->dump($order_id,'*',array('order_objects'=>array('*',array('order_items'=>array('*')))));
        $is_delete = false;

        if ($order['is_fail'] == 'true'){
            foreach($order['order_objects'] as $obj=>$items){
                if($items['goods_id'] <= 0 || empty($items['bn'])){
                    $is_delete = true;
                    break;
                }
                
                //不存在order_items 保持订单为失败订单
                if(!isset($items['order_items'])){
                    $is_delete = true;
                    break;
                }

                foreach($items['order_items'] as $key=>$item){
                    if($item['product_id']<=0 || !isset($item['product_id'])){
                        $is_delete = true;
                        break;
                    }
                }
            }

            //只要有个内容失败，这个订单还是失败订单，不变
            if($is_delete){
                $data = array('edit_status'=>'true');
                $orderObj->update($data,array('order_id' =>$order_id));
                return true;
            }
        }
        
        //修正订单
        $orderData = array();
        $orderData['is_modify'] = 'true';
        $orderData['is_fail'] = 'false';
        $orderData['archive'] = 0;
        $orderObj->update($orderData,array('order_id' =>$order_id));
        $affect_row = $orderObj->db->affect_row();
        if(is_numeric($affect_row) && $affect_row > 0){

            //修改明细打标
            $boolExtendStatus = ome_order_bool_extendstatus::__GOODS_PRICE;
            app::get('ome')->model('order_extend')->updateBoolExtendStatus($order_id, $boolExtendStatus);

            // 将平台上的规格更新到基础物料
            $this->updateBasicMaterialSpec($order_id);

            $order = array_merge($order, $orderData);
            $objRetrial = kernel::single('ome_order_retrial');
            list($rs, $msg) = $objRetrial->checkMonitorAbnormal($order);
            if($rs) {
                $objRetrial->monitorAbnormal($order['order_id'], $msg);
            }
            return true;
        }else{
            return false;
        }
    }

    // 将平台上的规格更新到基础物料
    public function updateBasicMaterialSpec($order_id)
    {
        $orderObj = app::get('ome')->model('order_objects');
        $orderObjList = $orderObj->getList('obj_id,addon', ['order_id' => $order_id]);

        $orderItemObj = app::get('ome')->model('order_items');
        foreach ($orderObjList as $orderObj) {
            if (empty($orderObj['addon'])) {
                continue;
            }
            $addon = json_decode($orderObj['addon'], true);
            if (empty($addon['product_attr'])) {
                continue;
            }
            $product_attr = $addon['product_attr'];
            $itemInfo = $orderItemObj->dump(['obj_id' => $orderObj['obj_id'], 'order_id' => $order_id], 'addon,item_id');
            if (empty($itemInfo)) {
                continue;
            }

            if (empty($itemInfo['addon'])) {
                $orderItemObj->update(['addon' => $product_attr], ['item_id' => $itemInfo['item_id'], 'obj_id' => $orderObj['obj_id'], 'order_id' => $order_id]);
            }
        }
        return true;
    }

    //单个修复订单
    public function modifyOrderItems($order_id,$oldPbn,$pbn,&$err_msg = ''){
        $err_msg = '存在异常商品，订单修正失败！';
        $orderObj = app::get('ome')->model('orders');
        //开启事务
        $orderObj->db->exec('begin');

        $itemObj = app::get('ome')->model('order_items');
        $Oorder_objects = app::get('ome')->model('order_objects');

        // [拆单]修复_淘宝平台_订单进入ERP的原始数据
        $modify_order_oid    = array();
        
        //先更新订单为不可编辑
        $data = array('edit_status'=>'false');
        $ret = $orderObj->update($data,array('order_id' =>$order_id, 'edit_status|noequal' => 'false', 'process_status'=>'unconfirmed'));
        if(is_bool($ret)) {//防止并发
            $orderObj->db->rollBack();
            return false;
        }
        $this->addFailOrderLog($order_id);//失败订单操作日志记录添加

        $orderInfo = $orderObj->getList('shop_id,ship_status, shop_type, order_bn,createtime',array('order_id'=>$order_id));

        if($pbn){
            $salesMLib = kernel::single('material_sales_material');
            $storeMdl = app::get('o2o')->model('store');
            $err_arr = [];
            foreach($pbn as $obj_id=>$bn){
                //获取对应的obj层数据
                $objInfo = $Oorder_objects->getList('*',array('obj_id'=>$obj_id), 0 , 1);
                //销售物料没对上，进行修复
                $salesMInfo = $salesMLib->getSalesMByBn($orderInfo[0]['shop_id'],$bn);
                if($salesMInfo){
                    $quantity = $objInfo[0]['quantity'] ? $objInfo[0]['quantity'] : 1;
                    if($salesMInfo['sales_material_type'] == 4){ //福袋
                        $basicMInfos = $salesMLib->get_order_luckybag_bminfo($salesMInfo['sm_id']);
                    }elseif($salesMInfo['sales_material_type'] == 5){ //多选一
                        $basicMInfos = $salesMLib->get_order_pickone_bminfo($salesMInfo['sm_id'],$quantity,$orderInfo[0]['shop_id']);
                    }else{
                        $basicMInfos = $salesMLib->getBasicMBySalesMId($salesMInfo['sm_id']);
                    }
                    if($basicMInfos){
                        //判断门店是否支持接单
//                        foreach($basicMInfos as $bv){
//                            $storeInfo = $storeMdl->dump(array('store_id' => $bv['store_id']), 'store_id,store_bn,is_receive_order,receive_order_start_time');
//                            if (empty($storeInfo) || $storeInfo['is_receive_order'] == 'false') {
//                                $msg = '基础物料编码[' . $bv['material_bn'] . ']，对应的门店';
//                                if (!empty($storeInfo['store_bn'])) {
//                                    $msg .= '[' . $storeInfo['store_bn'] . ']';
//                                }
//                                $msg .= '未开通接收订单';
//                                $err_arr[] = $msg;
//                                continue;
//                            }
//                            if ($storeInfo['receive_order_start_time'] && $orderInfo[0]['createtime'] < $storeInfo['receive_order_start_time']) {
//                                $msg .= '基础物料编码[' . $bn . ']，对应的门店未到接单时间，订单创建时间：' . date('Y-m-d H:i:s', $orderInfo[0]['createtime']) . ",门店接单时间：" . date('Y-m-d H:i:s', $storeInfo['receive_order_start_time']);
//                                $err_arr[] = $msg;
//                            }
//                        }


                        //如果是促销类销售物料
                        if($salesMInfo['sales_material_type'] == 2){
                            $obj_type = 'pkg';
                            //item层关联基础物料平摊销售价
                            $salesMLib->calProSaleMPriceByRate($objInfo[0]['sale_price'], $basicMInfos);
                            $price_rate = $salesMLib->calProPriceByRate($objInfo[0]['price'], $basicMInfos);
                            $pmt_price_rate = $salesMLib->calpmtpriceByRate($objInfo[0]['pmt_price'], $basicMInfos);
                            //

                            //组织item数据
                            foreach($basicMInfos as $k => $basicMInfo){
                                //$basicMInfo['retail_price'] ? $basicMInfo['retail_price'] : 0.00
                                //$price = $price_rate[$basicMInfo['material_bn']] ? bcdiv($price_rate[$basicMInfo['material_bn']]['rate_price'], $price_rate[$basicMInfo['material_bn']]['number'], 2) : 0.00;
                                $pmt_price  = $pmt_price_rate[$basicMInfo['material_bn']] ? $pmt_price_rate[$basicMInfo['material_bn']]['rate_price'] : 0.00;
                                $sale_price = $basicMInfo['rate_price'];
                                $amount     = bcadd($pmt_price, $sale_price,2);
                                $price = bcdiv($amount, $basicMInfo['number']*$quantity, 2);
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $basicMInfo['number']*$quantity;

                                $order_items[] = array(
                                    'shop_goods_id'   => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                    'product_id'      => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                    'shop_product_id' => $objInfo[0]['oid'] ? $objInfo[0]['oid'] : 0,
                                    'bn'              => $basicMInfo['material_bn'],
                                    'name'            => $basicMInfo['material_name'],
                                    'cost'            => $basicMInfo['cost'] ? $basicMInfo['cost'] : 0.00,
                                    'price'           => $price,
                                    'pmt_price'       => $pmt_price,
                                    'sale_price'      => $sale_price,
                                    'amount'          => $amount,
                                    'weight'          => $basicMInfo['weight'] ? $basicMInfo['weight'] : 0.00,
                                    'quantity'        => $basicMInfo['number']*$quantity,
                                    'addon'           => '',
                                    'item_type'       => 'pkg',
                                    'delete'          => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                    'sendnum'         => $sendnum,
                                    'fulfillment_store_id' => $basicMInfo['store_id'],
                                );

                                
                            }
                        }elseif($salesMInfo['sales_material_type'] == 4){ //福袋
                            $arr_luckybag_log = array();
                            $obj_type = 'lkb';
                            foreach($basicMInfos as $k => $basicMInfo){
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $basicMInfo['number']*$quantity;
                                $sale_price = $basicMInfo['price']*$basicMInfo['number']*$quantity;
                                $order_items[] = array(
                                    'shop_goods_id' => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                    'product_id' => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                    'shop_product_id' => $objInfo[0]['oid'] ? $objInfo[0]['oid'] : 0,
                                    'bn' => $basicMInfo['material_bn'],
                                    'name' => $basicMInfo['material_name'],
                                    'cost' => $basicMInfo['cost'] ? $basicMInfo['cost'] : 0.00,
                                    'price' => $basicMInfo['price'] ? $basicMInfo['price'] : 0.00,
                                    'pmt_price' => 0.00,
                                    'sale_price' => $sale_price ? $sale_price : 0.00,
                                    'amount' => $sale_price ? $sale_price : 0.00,
                                    'weight' => $basicMInfo['weight'] ? $basicMInfo['weight'] : 0.00,
                                    'quantity' => $basicMInfo['number']*$quantity,
                                    'addon' => '',
                                    'item_type' => 'lkb',
                                    'delete' => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                    'sendnum' => $sendnum,
                                    'fulfillment_store_id' => $basicMInfo['store_id'],
                                );
                                $arr_luckybag_log[] = array(
                                    "order_id" => $order_id,
                                    "shop_id" => $orderInfo[0]['shop_id'],
                                    "lbr_id" => $basicMInfo["lbr_id"],
                                    "sm_id" => $salesMInfo['sm_id'],
                                    "bm_id" => $basicMInfo['bm_id'],
                                    "num" => $basicMInfo['number']*$quantity,
                                    "price" => $basicMInfo['price'],
                                    "create_time" => time(),
                                ); 
                            }
                        }elseif($salesMInfo['sales_material_type'] == 5){ //多选一
                            $obj_sale_price = (isset($objInfo[0]['sale_price']) && is_numeric($objInfo[0]['sale_price']) && -1 != bccomp($objInfo[0]['sale_price'], 0, 3) ) ? $objInfo[0]['sale_price'] : bcsub($subtotal, (float)$item['pmt_price'],3);
                            $obj_type = 'pko';
                            //组织item数据
                            foreach($basicMInfos as $k => $basicMInfo){
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $basicMInfo['number'];
                                $order_items[] = array(
                                        'shop_goods_id' => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                        'product_id' => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                        'shop_product_id' => $objInfo[0]['oid'] ? $objInfo[0]['oid'] : 0,
                                        'bn' => $basicMInfo['material_bn'],
                                        'name' => $basicMInfo['material_name'],
                                        'cost' => (float)$objInfo[0]['cost'] ? $objInfo[0]['cost'] : $basicMInfo['cost'],
                                        'price' => (float)$objInfo[0]['price'],
                                        'pmt_price' => 0.00,
                                        'sale_price' => bcmul($obj_sale_price/$quantity, $basicMInfo['number'], 3),
                                        'amount' => bcmul($obj_sale_price/$quantity, $basicMInfo['number'], 3),
                                        'weight' => $basicMInfo['weight'] ? $basicMInfo['weight']*$basicMInfo['number']: 0.00,
                                        'quantity' => $basicMInfo['number'],
                                        'addon' => '',
                                        'item_type' => 'pko',
                                        'delete' => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                        'sendnum' => $sendnum,
                                        'fulfillment_store_id' => $basicMInfo['store_id'],
                                );
                            }
                        }else{
                            $obj_type = ($salesMInfo['sales_material_type'] == 1) ? 'goods' : 'gift';
                            $item_type = ($obj_type == 'goods') ? 'product' : 'gift';

                            if($obj_type == 'gift'){
                                //直接取object层的,不用做处理
                            }

                            //普通销售物料
                            foreach($basicMInfos as $k => $basicMInfo){
                                
                                $addon = '';
                                if ($objInfo[0]['product_attr']) {
                                    $addon = serialize(array('product_attr'=>$objInfo[0]['product_attr']));
                                }
                                $subtotal = $objInfo[0]['amount'] ? (float)$objInfo[0]['amount'] : bcmul((float)$objInfo[0]['price'], $quantity,3);
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $objInfo[0]['sendnum']*$basicMInfo['number'];

                                //普通销售物料item层数据以object为主,原本就是1:1的两层结构,item合并object,item层数据真实
                                $order_items[] = array(
                                    'shop_goods_id'   => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                    'product_id'      => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                    'shop_product_id' => $objInfo[0]['oid'] ? $objInfo[0]['oid'] : 0,
                                    'bn'              => $basicMInfo['material_bn'],
                                    'name'            => $basicMInfo['material_name'],
                                    'cost'            => (float)$objInfo[0]['cost'] ? $objInfo[0]['cost'] : $basicMInfo['cost'],
                                    'price'           => (float)$objInfo[0]['price'],
                                    'pmt_price'       => (float)$objInfo[0]['pmt_price'],
                                    'sale_price'      => (isset($objInfo[0]['sale_price']) && is_numeric($objInfo[0]['sale_price']) && -1 != bccomp($objInfo[0]['sale_price'], 0, 3) ) ? $objInfo[0]['sale_price'] : bcsub($subtotal, (float)$item['pmt_price'],3),
                                    'amount'          => $subtotal,
                                    'weight'          => (float)$objInfo[0]['weight'] ? $objInfo[0]['weight'] : ($basicMInfo['weight'] ? $basicMInfo['weight'] : 0.00),
                                    'quantity'        => $basicMInfo['number']*$quantity,
                                    'addon'           => $addon,
                                    'item_type'       => $item_type,
                                    'delete'          => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                    'sendnum'         => $sendnum,
                                    'fulfillment_store_id' => $basicMInfo['store_id'],
                                );
                            }
                        }
                    }else{
                        //找不到对应的基础物料
                        continue;
                    }
                    
                    //修复_淘宝平台_原始属性值
                    if($orderInfo[0]['shop_type'] == 'taobao')
                    {
                        $modify_order_oid[]    = array(
                                                            'order_id'=>$order_id,
                                                            'order_bn'=>$orderInfo[0]['order_bn'],
                                                            'obj_id'=>$objInfo[0]['obj_id'],
                                                            'old_bn'=>$oldPbn[$obj_id],
                                                            'new_bn'=>$salesMInfo['sales_material_bn'],
                                                        );
                    }
                    
                }else{
                    //找不到对应的销售物料
                    continue;
                }

                //原来两层结构都有问题失败的
                if($objInfo[0]['goods_id'] <= 0 || empty($objInfo[0]['bn'])){
                    $newOrder['order_objects'][] = array_merge($objInfo[0], array(
                        'obj_id' => $objInfo[0]['obj_id'],
                        'part_mjz_discount' => $objInfo[0]['part_mjz_discount'],
                        'divide_order_fee' => $objInfo[0]['divide_order_fee'],
                        'goods_id'      => $salesMInfo['sm_id'] ? $salesMInfo['sm_id'] : 0,
                        'bn'            => $bn ? $bn : null,
                        'obj_type'      => $obj_type,
                        'obj_alias'     => $this->_obj_alias[$obj_type] ? $this->_obj_alias[$obj_type] : '',
                        'order_items'   => $order_items,
                    ));

                    $obj_shop_freeze[$objInfo[0]['obj_id']] = $objInfo[0]['quantity'];
                    $obj_status[$objInfo[0]['obj_id']] = $objInfo[0]['delete'];
                }else{
                    //原来绑定基础物料item有问题失败的
                    $newOrder['order_objects'][] = array_merge($objInfo[0], array(
                        'obj_id' => $objInfo[0]['obj_id'],
                        'goods_id' => $salesMInfo['sm_id'] ? $salesMInfo['sm_id'] : 0,
                        'bn' => $salesMInfo['sales_material_bn'] ? $salesMInfo['sales_material_bn'] : null,
                        'part_mjz_discount' => $objInfo[0]['part_mjz_discount'],
                        'divide_order_fee' => $objInfo[0]['divide_order_fee'],
                        'order_items'   => $order_items,
                    ));
                }

                //注销上一轮的order_items
                unset($order_items);
            }
            //所属门店不接单报错
            if(!empty($err_arr)){
                $err_msg = implode(";", $err_arr);
                return false;
            }
        }
        
        if($newOrder){
            $basicMStockLib = kernel::single('material_basic_material_stock');
            $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');
            $needFreezeItem = [];
            foreach($newOrder['order_objects'] as $ok => $obj){
                foreach($obj['order_items'] as $ik =>$item){
                    $num = intval($item['quantity'])-intval($item['sendnum']);
                    if($item['product_id'] > 0 && $item['delete'] == 'false' && $num > 0){
                        $needFreezeItem[] = $item;
                    }

                    if($item['product_id'] > 0){
                        $basic_material_arr[] = $item['product_id'];
                    }
                }
            }
            if($needFreezeItem) {
                uasort($needFreezeItem, [kernel::single('console_iostockorder'), 'cmp_productid']);
                foreach($needFreezeItem as $item) {
                    $num = intval($item['quantity'])-intval($item['sendnum']);
                    //货品总冻结
                    $basicMStockLib->freeze($item['product_id'],$num);
                    $freezeData = [];
                    $freezeData['bm_id'] = $item['product_id'];
                    $freezeData['obj_type'] = material_basic_material_stock_freeze::__ORDER;
                    $freezeData['bill_type'] = 0;
                    $freezeData['obj_id'] = $order_id;
                    $freezeData['shop_id'] = $orderInfo[0]['shop_id'];
                    $freezeData['branch_id'] = 0;
                    $freezeData['bmsq_id'] = material_basic_material_stock_freeze::__SHARE_STORE;
                    $freezeData['num'] = $num;
                    $freezeData['obj_bn'] = $orderInfo[0]['order_bn'];
                    //订单级货品冻结
                    $resFreeze = $basicMStockFreezeLib->freeze($freezeData);
                    if($resFreeze == false){
                        //冻结预占流水添加失败,事务回滚
                        $orderObj->db->rollBack();
                        return false;
                    }
                }
            }
            //判断基础物料门店是否供货，供货的标记订单为全渠道订单
            if(app::get('o2o')->is_installed()){
                if($basic_material_arr){
                    
                    $basicMaterialLib    = kernel::single('material_basic_material');
                    $is_omnichannel      = $basicMaterialLib->isOmnichannelOrder($basic_material_arr);
                    if($is_omnichannel){
                        $newOrder['omnichannel'] = 1;
                    }
                }
            }else{
                unset($basic_material_arr);
            }

            $newOrder['order_id'] = $order_id;
            $newOrder = kernel::single('ome_order')->divide_objects_to_items($newOrder);
            $orderObj->save($newOrder);
        }

        // 如果有相同货号则修复失败
        $itemList = $itemObj->getList('bn', array('order_id' => $order_id));
        $countItemBn = array();
        foreach ($itemList as $item) {
            if (isset($countItemBn[$item['bn']])) {
                $orderObj->db->rollBack();
                return false;
            }
            $countItemBn[$item['bn']] = 1;
        }
        unset($countItemBn);
        
        //修正为正常订单
        if($this->modifyOrder($order_id)){
            //事务确认
            $orderObj->db->commit();
            //如果销售物料包含福袋log处理
            if(!empty($arr_luckybag_log)){
                $lib_material_luckybag = kernel::single('material_luckybag');
                foreach($arr_luckybag_log as $var_lu_lo){
                    $lib_material_luckybag->deal_luckybag_log($var_lu_lo);
                }
            }
            return true;
        }else{
            //修复失败 事务回滚
            $orderObj->db->rollBack();
            return false;
        }
    }

    public function modifyOrderItemsByBn($order_id,$oldPbn,$pbn, $modifyType = 'bn'){
        //防止并发修复订单
        $_inner_key = sprintf("fix_order_%s", md5($order_id));
        $aData = cachecore::fetch($_inner_key);
        if ($aData === false) {
            cachecore::store($_inner_key, 'fixed', 5);
        }else{//选中的失败订单已在修复中，请不要重复修复！！！如没有完成修复，请稍后重试！！！
            return false;
        }
        
        $orderObj = app::get('ome')->model('orders');
        //开启事务
        $orderObj->db->exec('begin');
        
        $itemObj = app::get('ome')->model('order_items');
        $Oorder_objects = app::get('ome')->model('order_objects');

        // [拆单]修复_淘宝平台_订单进入ERP的原始数据
        $modify_order_oid    = array();
        
        $data = array('edit_status'=>'false');
        $ret = $orderObj->update($data,array('order_id' =>$order_id, 'edit_status|noequal' => 'false', 'process_status'=>'unconfirmed'));
        if(is_bool($ret)) {//防止并发
            $orderObj->db->rollBack();
            return false;
        }

        $orderInfo = $orderObj->getList('shop_id,ship_status, shop_type, order_bn,is_fail',array('order_id'=>$order_id));

        if($oldPbn && $pbn && ($orderInfo[0]['is_fail'] == 'true')){
            $excludeObjIds = array();
            $salesMLib = kernel::single('material_sales_material');

            foreach($pbn as $key=>$bn){
                if (!$oldPbn[$key] || !$bn){
                    continue;
                }

                //获取对应的obj层数据
                $objectFilter = array('order_id'=>$order_id, 'goods_id'=>0);

                if ($modifyType == 'bn') {
                    $objectFilter['bn'] = $oldPbn[$key];
                } else {
                    $objectFilter['name'] = $oldPbn[$key];
                }

                // 过滤已处理子订单
                if (!empty($excludeObjIds)) {
                    $objectFilter['obj_id|notin'] = $excludeObjIds;
                }

                $objInfo = $Oorder_objects->getList('*', $objectFilter, 0 , 1);
                if(!$objInfo){
                    continue;
                }
                //销售物料没对上，进行修复
                $salesMInfo = $salesMLib->getSalesMByBn($orderInfo[0]['shop_id'],$bn);
                if($salesMInfo){
                    $quantity = $objInfo[0]['quantity'] ? $objInfo[0]['quantity'] : 1;
                    $order_items = array();
                    if($salesMInfo['sales_material_type'] == 4){
                        $basicMInfos = $salesMLib->get_order_luckybag_bminfo($salesMInfo['sm_id']);
                    }elseif($salesMInfo['sales_material_type'] == 5){ //多选一
                        $basicMInfos = $salesMLib->get_order_pickone_bminfo($salesMInfo['sm_id'],$quantity,$orderInfo[0]['shop_id']);
                    }else{
                        $basicMInfos = $salesMLib->getBasicMBySalesMId($salesMInfo['sm_id']);
                    }
                    if($basicMInfos){
                        //如果是促销类销售物料
                        if($salesMInfo['sales_material_type'] == 2){
                            $obj_type = 'pkg';
                            //item层关联基础物料平摊销售价
                            $salesMLib->calProSaleMPriceByRate($objInfo[0]['sale_price'], $basicMInfos);
                            //组织item数据
                            foreach($basicMInfos as $k => $basicMInfo){
                                //$basicMInfo['retail_price'] ? $basicMInfo['retail_price'] : 0.00
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $basicMInfo['number']*$quantity;
                                $order_items[] = array(
                                    'shop_goods_id'   => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                    'product_id'      => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                    'shop_product_id' => 0,
                                    'bn'              => $basicMInfo['material_bn'],
                                    'name'            => $basicMInfo['material_name'],
                                    'cost'            => $basicMInfo['cost'] ? $basicMInfo['cost'] : 0.00,
                                    'price'           => $basicMInfo['rate_price'] ? bcdiv($basicMInfo['rate_price'], $basicMInfo['number']*$quantity, 2) : 0.00,
                                    'pmt_price'       => 0.00,
                                    'sale_price'      => $basicMInfo['rate_price'] ? $basicMInfo['rate_price'] : 0.00,
                                    'amount'          => $basicMInfo['rate_price'] ? $basicMInfo['rate_price'] : 0.00,
                                    'weight'          => $basicMInfo['weight'] ? $basicMInfo['weight'] : 0.00,
                                    'quantity'        => $basicMInfo['number']*$quantity,
                                    'addon'           => '',
                                    'item_type'       => 'pkg',
                                    'delete'          => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                    'sendnum'         => $sendnum,
                                );
                            }
                        }elseif($salesMInfo['sales_material_type'] == 4){ //福袋
                            $arr_luckybag_log = array();
                            $obj_type = 'lkb';
                            foreach($basicMInfos as $k => $basicMInfo){
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $basicMInfo['number']*$quantity;
                                $sale_price = $basicMInfo['price']*$basicMInfo['number']*$quantity;
                                $order_items[] = array(
                                        'shop_goods_id' => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                        'product_id' => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                        'shop_product_id' => 0,
                                        'bn' => $basicMInfo['material_bn'],
                                        'name' => $basicMInfo['material_name'],
                                        'cost' => $basicMInfo['cost'] ? $basicMInfo['cost'] : 0.00,
                                        'price' => $basicMInfo['price'] ? $basicMInfo['price'] : 0.00,
                                        'pmt_price' => 0.00,
                                        'sale_price' => $sale_price ? $sale_price : 0.00,
                                        'amount' => $sale_price ? $sale_price : 0.00,
                                        'weight' => $basicMInfo['weight'] ? $basicMInfo['weight'] : 0.00,
                                        'quantity' => $basicMInfo['number']*$quantity,
                                        'addon' => '',
                                        'item_type' => 'lkb',
                                        'delete' => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                        'sendnum' => $sendnum,
                                );
                                $arr_luckybag_log[] = array(
                                    "order_id" => $order_id,
                                    "shop_id" => $orderInfo[0]['shop_id'],
                                    "lbr_id" => $basicMInfo["lbr_id"],
                                    "sm_id" => $salesMInfo['sm_id'],
                                    "bm_id" => $basicMInfo['bm_id'],
                                    "num" => $basicMInfo['number']*$quantity,
                                    "price" => $basicMInfo['price'],
                                    "create_time" => time(),
                                ); 
                            }
                        }elseif($salesMInfo['sales_material_type'] == 5){ //多选一
                            $obj_sale_price = (isset($objInfo[0]['sale_price']) && is_numeric($objInfo[0]['sale_price']) && -1 != bccomp($objInfo[0]['sale_price'], 0, 3) ) ? $objInfo[0]['sale_price'] : bcsub($subtotal, (float)$item['pmt_price'],3);
                            $obj_type = 'pko';
                            //组织item数据
                            foreach($basicMInfos as $k => $basicMInfo){
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $basicMInfo['number'];
                                $order_items[] = array(
                                        'shop_goods_id' => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                        'product_id' => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                        'shop_product_id' => 0,
                                        'bn' => $basicMInfo['material_bn'],
                                        'name' => $basicMInfo['material_name'],
                                        'cost' => (float)$objInfo[0]['cost'] ? $objInfo[0]['cost'] : $basicMInfo['cost'],
                                        'price' => (float)$objInfo[0]['price'],
                                        'pmt_price' => 0.00,
                                        'sale_price' => bcmul($obj_sale_price/$quantity, $basicMInfo['number'], 3),
                                        'amount' => bcmul($obj_sale_price/$quantity, $basicMInfo['number'], 3),
                                        'weight' => $basicMInfo['weight'] ? $basicMInfo['weight']*$basicMInfo['number']: 0.00,
                                        'quantity' => $basicMInfo['number'],
                                        'addon' => '',
                                        'item_type' => 'pko',
                                        'delete' => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                        'sendnum' => $sendnum,
                                );
                            }
                        }else{
                            $obj_type = ($salesMInfo['sales_material_type'] == 1) ? 'goods' : 'gift';
                            $item_type = ($obj_type == 'goods') ? 'product' : 'gift';

                            if($obj_type == 'gift'){
                                //直接取object层的,不用做处理
                            }
                            //普通销售物料
                            foreach($basicMInfos as $k => $basicMInfo){
                                
                                $addon = '';
                                if ($objInfo[0]['product_attr']) {
                                    $addon = serialize(array('product_attr'=>$objInfo[0]['product_attr']));
                                }
                                $subtotal = $objInfo[0]['amount'] ? (float)$objInfo[0]['amount'] : bcmul((float)$objInfo[0]['price'], $quantity,3);
                                $sendnum = $orderInfo[0]['ship_status'] == '0' ? 0 : $objInfo[0]['sendnum']*$basicMInfo['number'];

                                //普通销售物料item层数据以object为主,原本就是1:1的两层结构,item合并object,item层数据真实
                                $order_items[] = array(
                                    'shop_goods_id'   => $objInfo[0]['shop_goods_id'] ? $objInfo[0]['shop_goods_id'] : 0,
                                    'product_id'      => $basicMInfo['bm_id'] ? $basicMInfo['bm_id'] : 0,
                                    'shop_product_id' => $objInfo[0]['shop_product_id'] ? $objInfo[0]['shop_product_id'] : 0,
                                    'bn'              => $basicMInfo['material_bn'],
                                    'name'            => $basicMInfo['material_name'],
                                    'cost'            => (float)$objInfo[0]['cost'] ? $objInfo[0]['cost'] : $basicMInfo['cost'],
                                    'price'           => (float)$objInfo[0]['price'],
                                    'pmt_price'       => (float)$objInfo[0]['pmt_price'],
                                    'sale_price'      => (isset($objInfo[0]['sale_price']) && is_numeric($objInfo[0]['sale_price']) && -1 != bccomp($objInfo[0]['sale_price'], 0, 3) ) ? $objInfo[0]['sale_price'] : bcsub($subtotal, (float)$item['pmt_price'],3),
                                    'amount'          => $subtotal,
                                    'weight'          => (float)$objInfo[0]['weight'] ? $objInfo[0]['weight'] : ($basicMInfo['weight'] ? $basicMInfo['weight'] : 0.00),
                                    'quantity'        => $basicMInfo['number']*$quantity,
                                    'addon'           => $addon,
                                    'item_type'       => $item_type,
                                    'delete'          => ($objInfo[0]['delete'] == 'true') ? 'true' : 'false',
                                    'sendnum'         => $sendnum,
                                );
                            }
                        }
                    }else{
                        //找不到对应的基础物料
                        continue;
                    }
                    
                    //修复_淘宝平台_原始属性值
                    if($orderInfo[0]['shop_type'] == 'taobao')
                    {
                        $modify_order_oid[]    = array(
                                                            'order_id'=>$order_id,
                                                            'order_bn'=>$orderInfo[0]['order_bn'],
                                                            'obj_id'=>$objInfo[0]['obj_id'],
                                                            'old_bn'=>$oldPbn[$key],
                                                            'new_bn'=>$salesMInfo['sales_material_bn'],
                                                        );
                    }
                    
                }else{
                    //找不到对应的销售物料
                    continue;
                }

                //原来两层结构都有问题失败的
                if($objInfo[0]['goods_id'] <= 0 || empty($objInfo[0]['bn'])){
                    $newOrder['order_objects'][] = array_merge($objInfo[0], array(
                        'obj_id' => $objInfo[0]['obj_id'],
                        'part_mjz_discount' => $objInfo[0]['part_mjz_discount'],
                        'divide_order_fee' => $objInfo[0]['divide_order_fee'],
                        'goods_id'      => $salesMInfo['sm_id'] ? $salesMInfo['sm_id'] : 0,
                        'bn'            => $bn ? $bn : null,
                        'obj_type'      => $obj_type,
                        'obj_alias'     => $this->_obj_alias[$obj_type] ? $this->_obj_alias[$obj_type] : '',
                        'order_items'   => $order_items,
                    ));

                    $obj_shop_freeze[$objInfo[0]['obj_id']] = $objInfo[0]['quantity'];
                    $obj_status[$objInfo[0]['obj_id']] = $objInfo[0]['delete'];
                }else{
                    //原来绑定基础物料item有问题失败的
                    $newOrder['order_objects'][] = array_merge($objInfo[0], array(
                        'obj_id' => $objInfo[0]['obj_id'],
                        'goods_id' => $salesMInfo['sm_id'] ? $salesMInfo['sm_id'] : 0,
                        'bn' => $salesMInfo['sales_material_bn'] ? $salesMInfo['sales_material_bn'] : null,
                        'part_mjz_discount' => $objInfo[0]['part_mjz_discount'],
                        'divide_order_fee' => $objInfo[0]['divide_order_fee'],
                        'order_items'   => $order_items,
                    ));
                }

                //注销上一轮的order_items
                unset($order_items);

                // 补充已处理objid, 防止重复处理
                $excludeObjIds[] = $objInfo[0]['obj_id'];
            }
        }

        if($newOrder){
            $basicMStockLib = kernel::single('material_basic_material_stock');
            $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');
            $needFreezeItem = [];
            foreach($newOrder['order_objects'] as $ok => $obj){
                
                foreach($obj['order_items'] as $ik =>$item){
                    
                    $num = intval($item['quantity'])-intval($item['sendnum']);
                    
                    if($item['product_id'] > 0 && $item['delete'] == 'false' && $num > 0){
                        $needFreezeItem[] = $item;
                    }

                    if($item['product_id'] > 0){
                        $basic_material_arr[] = $item['product_id'];
                    }
                }
            }
            if($needFreezeItem) {
                uasort($needFreezeItem, [kernel::single('console_iostockorder'), 'cmp_productid']);
                foreach($needFreezeItem as $item) {
                    $num = intval($item['quantity'])-intval($item['sendnum']);
                    //货品总冻结
                    $basicMStockLib->freeze($item['product_id'],$num);
                    $freezeData = [];
                    $freezeData['bm_id'] = $item['product_id'];
                    $freezeData['obj_type'] = material_basic_material_stock_freeze::__ORDER;
                    $freezeData['bill_type'] = 0;
                    $freezeData['obj_id'] = $order_id;
                    $freezeData['shop_id'] = $orderInfo[0]['shop_id'];
                    $freezeData['branch_id'] = 0;
                    $freezeData['bmsq_id'] = material_basic_material_stock_freeze::__SHARE_STORE;
                    $freezeData['num'] = $num;
                    $freezeData['obj_bn'] = $orderInfo[0]['order_bn'];
                    //订单级货品冻结
                    $resFreeze = $basicMStockFreezeLib->freeze($freezeData);
                    if($resFreeze == false){
                        //冻结预占流水添加失败,事务回滚
                        $orderObj->db->rollBack();
                        return false;
                    }
                }
            }

            //判断基础物料门店是否供货，供货的标记订单为全渠道订单
            if(app::get('o2o')->is_installed()){
                if($basic_material_arr){
                    
                    $basicMaterialLib    = kernel::single('material_basic_material');
                    $is_omnichannel      = $basicMaterialLib->isOmnichannelOrder($basic_material_arr);
                    if($is_omnichannel){
                        $newOrder['omnichannel'] = 1;
                    }
                }
            }else{
                unset($basic_material_arr);
            }

            $newOrder['order_id'] = $order_id;
            $newOrder = kernel::single('ome_order')->divide_objects_to_items($newOrder);
            $orderObj->save($newOrder);
        }

        // 如果有相同货号则修复失败
        $itemList = $itemObj->getList('bn', array('order_id' => $order_id));
        $countItemBn = array();
        foreach ($itemList as $item) {
            if (isset($countItemBn[$item['bn']])) {
                $orderObj->db->rollBack();
                return false;
            }
            $countItemBn[$item['bn']] = 1;
        }
        unset($countItemBn);
        
        //修复_淘宝平台_原始属性值
        if($modify_order_oid)
        {
            $this->modifyOrderOid($modify_order_oid);
        }

        //修正为正常订单
        if($this->modifyOrder($order_id)){
            //事务确认
            $orderObj->db->commit();
            //如果销售物料包含福袋log处理
            if(!empty($arr_luckybag_log)){
                $lib_material_luckybag = kernel::single('material_luckybag');
                foreach($arr_luckybag_log as $var_lu_lo){
                    $lib_material_luckybag->deal_luckybag_log($var_lu_lo);
                }
            }
            return true;
        }else{
            //修复失败 事务回滚
            $orderObj->db->rollBack();
            return false;
        }
    }

    /**
     * 失败订单操作日志记录添加
     *
     * @return void
     * <AUTHOR>
    function addFailOrderLog($order_id,$opinfo=NULL)
    {
        $oLog = app::get('ome')->model('operation_log');

        $log_id = $oLog->write_log('order_edit@ome',$order_id,"失败订单恢复",'',$opinfo);

        $orderObj = app::get('ome')->model('orders');
        $opObj = app::get('ome')->model('order_pmt');
        $membersObj = app::get('ome')->model('members');
        $paymentsObj = app::get('ome')->model('payments');
        $orders = $orderObj->dump(array('order_id'=>$order_id),"*",array("order_objects"=>array("*",array("order_items"=>array('*')))));

        //优惠方案
        $orders['pmt'] = $opObj->getList('*',array('order_id'=>$order_id));//订单优惠方案
        //会员信息
        $orders['mem_info'] = $membersObj->getRow($orders['member_id']);
        //支付单
        $orders['payments'] = $paymentsObj->getList('*',array('order_id'=>$order_id));

        $orderObj->write_log_detail($log_id,$orders);
    }

    public function getFailOrderByBn($bn=array()){
        $orderObj = app::get('ome')->model('orders');
        $sql = 'SELECT I.order_id FROM sdb_ome_order_objects as I LEFT JOIN '.
            'sdb_ome_orders as O ON I.order_id=O.order_id WHERE O.is_fail=\'true\' and O.edit_status=\'true\' and O.archive=\'1\' and I.goods_id=\'0\' and I.bn in (\''.implode('\',\'',$bn).'\') GROUP BY order_id';
        $rows = $orderObj->db->select($sql);
        return $rows;
    }
    
    public function getFailOrderByName($name=array()){
        $orderObj = app::get('ome')->model('orders');

        $sql = 'SELECT I.order_id FROM sdb_ome_order_objects as I LEFT JOIN '.
            'sdb_ome_orders as O ON I.order_id=O.order_id WHERE O.is_fail=\'true\' and O.edit_status=\'true\' and O.archive=\'1\' and I.goods_id=\'0\' and I.name in (\''.implode('\',\'',$name).'\') GROUP BY order_id';

        $rows = $orderObj->db->select($sql);

        return $rows;
    }


    /**
     * 修复[淘宝平台]原始属性值
     * PS:拆单开启后,订单部分回写会使用
     *
     * @param  Array    $modify_order_oid
     * @return void
     **/
    function modifyOrderOid($modify_order_oid)
    {
        if(empty($modify_order_oid))
        {
            return false;
        }
        
        $orderDlyObj    = app::get('ome')->model('order_delivery');
        foreach ($modify_order_oid as $item_id => $item)
        {
            $getData    = $bn_data = array();
    
            //获取淘宝平台的原始数据
            $getData    = $orderDlyObj->dump(array('order_bn'=>$item['order_bn']), 'id, bn');
            
            if(empty($getData['bn']))
            {
                continue;
            }
            
            $bn_data   = unserialize($getData['bn']);
            foreach ($bn_data as $key => $val)
            {
                if($val == $item['old_bn'])
                {
                    $bn_data[$key]    = $item['new_bn'];
                }
            }
            $getData['bn']    = serialize($bn_data);
            
            $orderDlyObj->save($getData);
            
            unset($getData, $bn_data);
        }
        
        return true;
    }
}
