<?php

class ome_sap_data_platform_wxshipin extends ome_sap_data_abstract
{
    /**
     * 默认支付方式
     * @var string
     */
    protected $default_pay_name = '微信';

    /**
     * 优惠券类型映射
     * @var array
     */
    protected $coupon_type_mapping = [
        '1' => '商品条件折扣券',
        '2' => '商品满减券',
        '3' => '商品统一折扣券',
        '4' => '商品直减券',
        '101' => '店铺条件折扣券',
        '102' => '店铺满减券',
        '103' => '店铺统一折扣券',
        '104' => '店铺直减券',
    ];

    /**
     * 不推送金蝶sap的券类型
     * @var string[]
     */
    protected $notPushCouponTypeList = ['deduction'];

    /**
     * 获取oid
     * @param $order_id
     * @param $oid
     * @return void
     */
    public function getOid($order_id, $oid)
    {
        return $order_id . $oid;
    }

    public function get_payments($params)
    {
        if (empty($params)) {
            return false;
        }

        $paymentsMdl = app::get('ome')->model('payments');
        $result = ['payment' => [], 'platform' => []];

        # 设定支付明细（含运费）
        $realPaymentList = $this->getRealPaymentsList($params);
        if (!empty($realPaymentList['payment'])) {
            # 获取订单支付方式
            $paymentList = $paymentsMdl->getList('pay_bn,paymethod,money', array('order_id' => $params['order_id']));
            # 判断是否存在多种支付方式
            if (!empty($paymentList) && count($paymentList) > 1) {
                # 多种支付方式，需要按多种支付方式分摊到商品明细上
                $total_amount = array_sum(array_column($paymentList, 'money'));
                # 按sku明细分摊
                foreach ($realPaymentList['payment'] as $item) {
                    # 分摊金额
                    $split_amount = $item['money'];
                    $split_count = count($paymentList);
                    foreach ($paymentList as $k => $row) {
                        if ($k + 1 == $split_count) {
                            # 加入支付明细
                            $item['money'] = $split_amount;
                            $item['payment'] = $row['pay_bn'];      // 支付编码
                            $item['paymethod'] = $row['paymethod']; // 支付名称
                            $paymentItems[] = $item;
                        } else {
                            # 支付方式金额占比
                            $payment_rate = bcdiv($row['money'], $total_amount, 6);
                            # 重新计算分摊金额
                            $payment_amount = bcmul($item['money'], $payment_rate, 2);
                            # 加入支付明细
                            $item['money'] = $payment_amount;
                            $item['payment'] = $row['pay_bn'];      // 支付编码
                            $item['paymethod'] = $row['paymethod']; // 支付名称
                            $paymentItems[] = $item;
                            # 重新计算剩余分摊金额
                            $split_amount = bcsub($split_amount, $payment_amount, 2);
                        }
                    }
                }

                # 合并支付明细
                if (!empty($paymentItems)) {
                    $realPaymentList['payment'] = $paymentItems;
                }
            } else {
                # 只有一种支付方式，直接使用支付单上面的支付方式和编码
                foreach ($realPaymentList['payment'] as $k => $item) {
                    if ($item['coupon_type'] != 'payment') {
                        continue;
                    }
                    $realPaymentList['payment'][$k]['payment'] = $paymentList[0]['pay_bn'];      // 支付编码
                    // 支付名称
                    $pay_name = $paymentList[0]['paymethod'] ?? $params['payinfo']['pay_name'];
                    // 没有支付名称，设置为默认支付方式
                    if (empty($pay_name)) {
                        $pay_name = $this->default_pay_name;
                    }
                    $realPaymentList['payment'][$k]['paymethod'] = $pay_name;
                }
            }
            $result['payment'] = $realPaymentList['payment'];
        }

        # 平台优惠券、活动相关
        $plateformList = $this->getPlateformPromotion($params['order_id']);
        if (!empty($plateformList)) {
            foreach ($plateformList as $item) {
                $plateform = [
                    'oid' => $item['oid'],
                    'type' => 'platform',
                    'money' => floatval($item['money']),
                    'paymethod' => $item['paymethod'],
                    'sub_type' => $item['sub_type'],
                    'coupon_type' => $item['coupon_type'],
                    'category' => $item['category'],
                ];
                # 券号
                if (!empty($item['coupon_code'])) {
                    $plateform['coupon_code'] = $item['coupon_code'];
                }
                $result['platform'][] = $plateform;
            }
        }

        # 合并数据
        $result = array_merge($result['payment'], $result['platform']);
        return $result;
    }

    /**
     * 优惠券信息
     * @param $order_id
     * @return void
     */
    public function get_coupons($order_id, $oid = [])
    {
        if (empty($order_id)) {
            return false;
        }

        $couponMdl = app::get('ome')->model('order_coupon_wxshipin');
        # 获取所有优惠列表
        $filter = [
            'order_id' => $order_id,
            'type' => 'coupon'
        ];
        if (!empty($oid)) {
            $filter['oid'] = $oid;
        }
        $couponList = $couponMdl->getList('*', $filter);
        if (empty($couponList)) {
            return false;
        }

        # 获取优惠券参数
        $result = $this->_format_coupon_params($order_id, $couponList);
        return $result;
    }

    /**
     * 根据优惠券id获取优惠券列表
     * @param $order_id
     * @param $coupon_id
     * @return void
     */
    public function get_palateform_coupons($order_id, $coupon_id = [])
    {
        if (empty($order_id)) {
            return false;
        }

        $couponMdl = app::get('ome')->model('order_coupon_wxshipin');
        # 获取所有优惠列表
        $filter = [
            'order_id' => $order_id,
            'type' => 'coupon'
        ];
        if (!empty($coupon_id)) {
            $filter[$this->coupon_field_name] = array_unique($coupon_id);
        }
        $couponList = $couponMdl->getList('*', $filter);
        if (empty($couponList)) {
            return false;
        }

        # 获取优惠券参数
        $result = $this->_format_coupon_params($order_id, $couponList);
        return $result;
    }

    /**
     * 获取优惠券参数
     * @param $couponList
     * @return mixed
     */
    private function _format_coupon_params($order_id, $couponList)
    {
        if (empty($couponList)) {
            return [];
        }

        $result = [];
        foreach ($couponList as $coupon) {
            # 过滤没有券码的优惠券
            if (empty($coupon['coupon_id'])) {
                continue;
            }
            # 优惠券参数
            $data = [
                'eshopOrderSn' => $this->getOid($order_id, $coupon['oid']),
                'couponCode' => $coupon[$this->coupon_field_name],  // 券ID，非用户券ID
                'couponName' => $coupon['coupon_name'],
                'couponAmount' => $coupon['coupon_amount'],
            ];
            if (in_array($coupon['coupon_type'], array('2', '4', '102'))) {
                $data['couponType'] = '小镇';
            } elseif ($coupon['platform_type'] == 'shop_discount' && $coupon['coupon_type'] == '0') {  // 商家优惠（非优惠券）
                $data['couponType'] = '小镇';
            } else {
                $data['couponType'] = '未知';
            }
//            switch ($coupon['coupon_type']) {
//                case 'shop_discount': // 商家优惠
//                    $data['couponType'] = '小镇';
//                    break;
//                case 'kol_discount': // 达人优惠
//                    $data['couponType'] = '达人';
//                    break;
//            }

            # 按couponCode+couponType合并
            $key = sprintf('%s_%s', $data['couponCode'], $data['couponType']);
            if (isset($result[$key])) {
                $result[$key]['couponAmount'] = bcadd($result[$key]['couponAmount'], $data['couponAmount'], 2);
            } else {
                $result[$key] = $data;
            }
        }
        return array_values($result);
    }

    /**
     * 平台活动、优惠相关
     * @param $order_id
     * @return mixed
     */
    protected function getPlateformPromotion($order_id)
    {
        if (empty($order_id)) {
            return false;
        }

        $couponMdl = app::get('ome')->model('order_coupon_wxshipin');
        # 获取所有优惠列表
        $filter = [
            'order_id' => $order_id,
            'type' => ['discount', 'coupon', 'adjust', 'deduction'], // 优惠、优惠券、商家改价、积分兑换
        ];
        $couponList = $couponMdl->getList('*', $filter);
        if (empty($couponList)) {
            return false;
        }

        $result = array();
        foreach ($couponList as $coupon) {
            # 积分兑换不传递ESB
            if (!empty($this->notPushCouponTypeList) && in_array($coupon['type'], $this->notPushCouponTypeList)) {
                continue;
            }
            # 推广类型=1的优惠券不传递ESB
            if ($coupon['type'] == 'coupon' && $coupon['sub_type'] == '1') {
                continue;
            }

            $data = [
                'oid' => $this->getOid($order_id, $coupon['oid']),
                'category' => $coupon['platform_type'],  // 所属分类
                'money' => $coupon['coupon_amount'],
                'sub_type' => $coupon['type'],
                'coupon_type' => $coupon['coupon_type'],
                'paymethod' => $coupon['coupon_name'],
            ];
            # 优惠券
            if ($coupon['type'] == 'coupon' && !empty($coupon[$this->coupon_field_name])) {
                $data['coupon_code'] = $coupon[$this->coupon_field_name];
            }
            $result[] = $data;
        }
        return $result;
    }

    /**
     * 获取订单明细的实付明细
     * @param $params
     * @return mixed
     */
    private function getRealPaymentsList($params)
    {
        if (empty($params['order_objects'])) {
            return false;
        }

        $result = ['payment' => []];
        # 获取sku实付金额
        foreach ($params['order_objects'] as $object) {
//            if ($object['delete'] == 'true' || floatval($object['divide_order_fee']) <= 0) {
//                continue;
//            }

            $payment = [
                'type' => 'payment',
                'money' => $object['divide_order_fee'],
                'paymethod' => '买家支付金额',
                'sub_type' => 'payment',
                'coupon_type' => 'payment',
                'oid' => $this->getOid($object['order_id'], $object['oid'])
            ];
            $result['payment'][] = $payment;
        }
        return $result;
    }

    /**
     * 是否0元抽奖订单
     * @param $order_id
     * @return bool
     */
    protected function is_lucky_order($orderInfo)
    {
        if (empty($orderInfo)) {
            return false;
        }

        # 判断订单的支付方式是不是：抽奖0元购
        if (ome_func::contains($orderInfo['payinfo']['pay_name'], '抽奖0元购')) {
            return true;
        }
        return false;
    }

    /**
     * 获取订单参数
     * @param $order_id
     * @param $extend
     * @return mixed
     */
    public function get_order_sdf($order_id, $extend = [])
    {
        $result = parent::get_order_sdf($order_id, $extend);
        if (false === $result) {
            return false;
        }

        # 订单明细总优惠金额和总实付金额
        $totoal_part_mjz_discount = 0;
        foreach ($result['order']['order_objects'] as $object) {
            foreach ($object['order_items'] as $item) {
                $totoal_part_mjz_discount = bcadd($totoal_part_mjz_discount, $item['part_mjz_discount'], 2);
            }
        }
        # 订单表的总金额和总优惠
        $order_total_pmt_amount = bcadd($result['order']['pmt_goods'], $result['order']['pmt_order'], 2);
        # 获取esb优惠列表
        $esb_discount_list = [];
        $default_is_payment = $extend['is_payment'] ?? true;
        if ($default_is_payment) {
            foreach ($result['order']['payments'] as $payment) {
                # 过滤支付
                if ($payment['type'] == 'payment') {
                    continue;
                }
                $esb_discount_list[] = $payment;
            }
        }
        if (floatval($order_total_pmt_amount) == 0 && floatval($totoal_part_mjz_discount) > 0 && ($default_is_payment && empty($esb_discount_list))) {
            # 一起买订单
            $result['order']['is_group_buy_order'] = true;
        }
        return $result;
    }
    
}