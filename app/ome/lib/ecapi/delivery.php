<?php

class ome_ecapi_delivery extends ome_ecapi_api{

    /**
     * 同步商城发货单揽收、签收状态
     * @param $delivery_id
     * @param $logi_status
     * @param string $err_msg
     * @return bool
     */
    public function sync_logi_status($delivery_id, $logi_status, &$err_msg = ''){
        //1已揽收 3已签收
        $dMdl = app::get("ome")->model("delivery");
        $doMdl = app::get("ome")->model("delivery_order");
        $oMdl = app::get("ome")->model("orders");
        $dInfo = $dMdl->dump($delivery_id, "*");
        $opLogMdl = app::get('ome')->model('operation_log');
        if($dInfo['status'] != 'succ'){
            $err_msg = '发货单不是已发货状态，不支持同步物流状态';
            return false;
        }
        $config  = $this->get_config($dInfo['shop_id']);
        if(!$config){
            $err_msg = "店铺直连接口未配置";
            return false;
        }
        if(!$config['direct_api_url']){
            $err_msg = "店铺未配置直连API地址";
            return false;
        }
        $doInfo = $doMdl->dump(array("delivery_id" => $delivery_id), "order_id");
        $oInfo = $oMdl->dump($doInfo['order_id'], "order_id,order_bn");
        $params = array(
            'to_node_id' => $config['node_id'],
            'act' => $this->get_act_type('sync_logi_status'),
            'order_id' => $oInfo['order_bn'],
            'logi_no' => $dInfo['logi_no'],
        );
        switch ($logi_status){
            case '1':
                $params['type'] = 'collected';
                $title = "发货单揽收状态同步前端店铺";
                break;
            case '3':
                $params['type'] = 'signed';
                $title = "发货单签收状态同步前端店铺";
                break;
            default:
                $err_msg = '不支持同步的状态值'.$logi_status;
                return false;
        }
        $params['ac'] = $this->gen_sign($params, $config['direct_api_token']);
        $res = $this->curl($config['direct_api_url'], $params);
        if($res && $res['result'] == 'success'){
            $this->write_log($title, $oInfo['order_bn'], 'success', $params, $params, $res);
            $opLogMdl->write_log('delivery_modify@ome',$delivery_id,$title."成功");
            return true;
        }
        $this->write_log($title, $oInfo['order_bn'], 'fail', $params, $params, $res);
        $opLogMdl->write_log('delivery_modify@ome',$delivery_id,$title."失败");

        return false;
    }

    /**
     * 更改物流单号
     * @param $params
     * @param string $err_msg
     * @return bool
     */
    public function update_delivery_code($sdf, &$err_msg = ''){

        $config  = $this->get_config($sdf['shop_id']);
        if(!$config){
            $err_msg = "店铺直连接口未配置";
            return false;
        }

        if(!$config['direct_api_url']){
            $err_msg = "店铺未配置直连API地址";
            return false;
        }

        $params = array(
            'to_node_id' => $config['node_id'],
            'act' => $this->get_act_type('update_delivery_code'),
            'order_id' => $sdf['order_bn'],
            'old_code' => $sdf['old_code'],
            'new_code' => $sdf['new_code'],
        );

        $opLogMdl = app::get('ome')->model('operation_log');
        $params['ac'] = $this->gen_sign($params, $config['direct_api_token']);
        $res = $this->curl($config['direct_api_url'], $params);
        $title = "商城更改物流单号";
        if($res && $res['result'] == 'success'){
            $this->write_log($title, $sdf['order_bn'], 'success', $params, $params, $res);
            $opLogMdl->write_log('delivery_modify@ome',$sdf['delivery_id'],$title."成功");
            return true;
        }
        $this->write_log($title, $sdf['order_bn'], 'fail', $params, $params, $res);
        $opLogMdl->write_log('delivery_modify@ome',$sdf['delivery_id'],$title."失败");

        return false;
    }

}