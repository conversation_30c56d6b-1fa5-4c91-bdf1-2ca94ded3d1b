<{capture name="header"}>
  <link href="../apps/ome/statics/ome.css" rel="stylesheet" type="text/css">
<{/capture}>
<h4 class="head-title">前端店铺</h4>
<div class="tableform">
           <div class="division">
<form method="post" action="index.php?app=ome&ctl=admin_shop&act=saveterminal" id="terminal">
      <input type="hidden" id="shop_id"   name="shop[shop_id]" value="<{$shop.shop_id}>" />
      <table width="100%" cellspacing="0" cellpadding="0" border="0" >
        <tbody>
       <tr>
          <th><em class="c-red">*</em> 店铺编码：</th>
          <td><{if $shop.shop_bn}><{$shop.shop_bn}><{else}><{input type='text&&required' name="shop[shop_bn]" value=$shop.shop_bn size="60"}><{/if}>
          <input type='hidden' name="shop[old_shop_bn]" value="<{$shop.shop_bn}>" >
          <{help}><{t}>前端网店在系统中的唯一标识<{/t}><{/help}>
          </td>
        </tr>
        <tr>
        <th ><em class="c-red">*</em>店铺名称：</th>
          <td><{input type="text&&required" size="60" name="shop[name]" value=$shop.name }>
          <{help}><{t}>前端网店在此系统需要显示的中文名称<{/t}><{/help}>
          </td>
        </tr>
        <tr>
        <th ><em class="c-red">*</em> 运营组织：</th>
          <td>
              <{if $shop.org_name}>
                  <{$shop.org_name}>    <em class="c-red">（已配置运营组织，不允许更改）</em>
              <{else}>
                  <select name='shop[org_id]'>
                  <{foreach from=$orgs item=item}>
                  <option value="<{$item.org_id}>" <{if $shop.org_id == $item.org_id}>selected<{/if}> ><{$item.name}></option>
                  <{/foreach}>
                  </select>
              <{/if}>
          <{help}><{t}>定义后可针对订单、售后、单据等细分权限进行管理<{/t}><{/help}>
          </td>
        </tr>
        <tr>
          <th><em class="c-red">*</em> 地区：</th>
          <td><{input type='region' app='eccommon' name="shop[area]" value=$shop.area vtype="area" }>
          <{help}><{t}>用来联系及打印在“快递单”中发货点所在省/市/县（区）<{/t}><{/help}>
          </td>
        </tr>
        <tr>
          <th><em class="c-red">*</em> 地址：</th>
          <td><input class="x-input" type="text" vtype="required" name="shop[addr]" size="60" value="<{$shop.addr}>"/>
          <{help}><{t}>用来联系及打印在“快递单”中发货点的发货地址<{/t}><{/help}>

          </td>
        </tr>
		<tr>
          <th><em class="c-red">*</em> 邮编：</th>
          <td><input class="x-input" type="text" vtype="required" id="zip" name="shop[zip]" size="60" maxlength="6" value="<{$shop.zip}>"/>
          <{help}><{t}>用来联系及打印在“快递单”中发货点所在地区的邮政编号<{/t}><{/help}>
          </td>
        </tr>
        <tr>
          <th><em class="c-red">*</em> 发件人：</th>
          <td><input class="x-input" type="text" vtype="required" name="shop[default_sender]" size="60" value="<{$shop.default_sender}>"/>
          <{help}><{t}>用来联系及打印在“快递单”中发货点负责人的姓名<{/t}><{/help}>
          </td>
        </tr>
        <tr>
          <th>固定电话：</th>
          <td><input class="x-input" type="text" id="shop[tel]" name="shop[tel]" size="60" value="<{$shop.tel}>"/>
          <{help}><{t}>用来联系及打印在“快递单”中发货点的固定电话，卖家固定电话格式为：区号+电话+分机号(没有可不填)，中间用"-"隔开; 卖家固定电话和卖家手机号码,必须填写一个. <{/t}><{/help}>
          </td>
        </tr>
        <tr>
          <th>手机：</th>
          <td><input class="x-input" type="text" id="mobile" name="shop[mobile]" size="60" value="<{$shop.mobile}>"/>
          <{help}><{t}>用来联系及打印在“快递单”中发货点的移动联系方式；号码前请不要加0<{/t}><{/help}>
          </td>
        </tr>
		<tr>
           <th>联系人邮箱：</th>
           <td><input type="text" name="shop[email]" value="<{$shop.email}>"/>
               <{help}><{t}>邮箱名#收件人(多个收件邮箱之间请用;隔开) 例：<EMAIL>#小王;<EMAIL>#小张<{/t}><{/help}>
           </td>
       </tr>
      <tr>
           <th>开始接单时间：</th>
            <td><{input type='time' name='start_order_time' value=$shop.start_order_time}>
               <{help}><{t}>设置店铺开始接单时间，订单创建时间小于接单时间的系统不接收<{/t}><{/help}>
           </td>
       </tr>

        <tr>
          <th>发货模式：</th>
          <td>
            <input type="radio" name="shop[delivery_mode]" value="self" <{if $shop.delivery_mode == 'self' || empty($shop.delivery_mode)}>checked="checked"<{/if}> />自发货
            &nbsp;&nbsp;&nbsp;
            <input type="radio" name="shop[delivery_mode]" value="jingxiao" <{if $shop.delivery_mode == 'jingxiao'}>checked="checked"<{/if}>>经销发货
            <{help}><{t}>经销发货将会把订单改为平台自发<{/t}><{/help}>
          </td>
        </tr>
        <tr>
          <th>业务分类：</th>
          <td>
            <input type="radio" name="shop[business_category]" value="B2C" <{if $shop.business_category == 'B2C' || empty($shop.business_category)}>checked="checked"<{/if}> />B2C
            &nbsp;&nbsp;&nbsp;
            <input type="radio" name="shop[business_category]" value="B2B" <{if $shop.business_category == 'B2B'}>checked="checked"<{/if}>>B2B
          </td>
        </tr>
        <tr>
          <th> 网店地址：</th>
          <td><{input type="text" size="60" name="shop[config][url]" value=$shop_config.url}>
         <{help}><{t}>前端网店在第三方平台的唯一标识（店铺的链接地址）<{/t}><{/help}>
          </td>
        </tr>
        <tr class="x-ex" style="display:none">
          <th><em class="c-red">*</em> 账号：</th>
          <td><{input type="text&&required" name="shop[config][account]" size="32" value=$shop_config.account}>
          <{help}><{t}>登陆前端网店的账号，系统用来与前端网店数据同步使用<{/t}><{/help}> 
          
          </td>
        </tr>
        <tr class="x-ex" style="display:none">
          <th><em class="c-red">*</em> 密码：</th>
          <td><{input type="password" name="shop[config][password]" size="20" value=$shop_config.password required="true"}>
            <{help}><{t}> 登陆前端网店的密码，系统用来与前端网店数据同步使用<{/t}><{/help}>
         
          </td>
        </tr>


       <tr>
           <th >渠道适配器：</th>
           <td>
               <{if $shop.node_id }>
               <{input type='select' disabled="true" name='shop[config][adapter]' id='wmstype' rows=$adapter_list valueColumn='value' labelColumn='label' value=$shop_config.adapter required='true'}>
               <{else}>
               <{input type='select'  name='shop[config][adapter]'  rows=$adapter_list valueColumn='value' labelColumn='label' value=$shop_config.adapter required='true'}>
               <{/if}>
               <ul style='color:red' id="adapter_desc">
                   <{foreach from=$adapter_list item=item}>
                   <li id="desc_<{$item.value}>" style="display:none"><{$item.desc}></li>
                   <{/foreach}>
               </ul>
           </td>
       </tr>
       <tr>
           <th> 直连接口地址：</th>
           <td><{input type="text" size="60" name="shop[direct_api_url]" value=$shop.direct_api_url}>
               <{help}><{t}>直连EC商城的接口地址<{/t}><{/help}>
           </td>
       </tr>
       <tr>
           <th> 直连接口token：</th>
           <td><{input type="text" size="60" name="shop[direct_api_token]" value=$shop.direct_api_token}>
               <{help}><{t}>直连EC商城的token<{/t}><{/help}>
           </td>
       </tr>

        </tbody>
      </table>
    <{if $shop.shop_id}>
 <input type="hidden" name="shop[shop_id]" value="<{$shop.shop_id}>">
<{/if}>
<{if $shop.node_id}>
 <input type="hidden" name="shop[node_id]" value="<{$shop.node_id}>">
<{/if}>
<div class="table-action">
<{button class="btn-primary" type="button" id="saveterminal" name="submit" label="提交"}>

</div>
</form>
  </div>
    </div>

<script>

function is_phone(str){

	var partten = /^\d{1,4}-\d{7,8}(-\d{1,6})?$/;
    var partten1 = /^400\d{7}$/;
    if(partten.test(str) || partten1.test(str)){
		return true;
	}else{
		return false;
	}
}

function is_mobile(str){

	var partten = /^\d{8,15}$/;
	if(partten.test(str)){
		return true;
	}else{
		return false;
	}
}

$('saveterminal').addEvent('click',function(event){
	if ($('zip').value.length != "6"){
		   alert('请输入正确的邮编');
		   $('zip').focus();
		   return false;
    }
    //固定电话与手机必填一项
    var gd_tel,mobile;
    gd_tel = $('shop[tel]').value.replace(" ","");
    mobile = $('mobile').value.replace(' ','');
    if (!gd_tel && !mobile){
		   alert('固定电话与手机号码必需填写一项');
		   $('shop[tel]').focus();
		   return false;
    }
    if (gd_tel){
			if (is_phone(gd_tel) === false){
				alert('请填写正确的固定电话');
			    $('shop[tel]').focus();
			    return false;
			}
    }
    if (mobile){
		   if ( is_mobile(mobile) === false){
			    alert('请输入正确的手机号码');
			    $('mobile').focus();
			    return false;
		   }
		   if (mobile[0] == '0'){
			    alert('手机号码前请不要加0');
			    $('mobile').focus();
			    return false;
		   }
    }
    $('terminal').fireEvent('submit',new Event(event));
});

$('terminal').store('target',{
    onRequest:function(){
       $('saveterminal').set('disabled', 'true');
    },
    onComplete:function(jsontext){
       var json = Json.evaluate(jsontext);
       if (typeof(json.error) != 'undefined'){
           $('saveterminal').set('disabled', '');
       }else{
           $('saveterminal').set('disabled', 'true');
           opener.finderGroup['<{$env.get.finder_id}>'].refresh.delay(400,opener.finderGroup['<{$env.get.finder_id}>']);
           window.close();
       }
    }
    });


</script>