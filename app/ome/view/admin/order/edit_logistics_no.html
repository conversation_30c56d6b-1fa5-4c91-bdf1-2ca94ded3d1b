<link rel="stylesheet" type="text/css" href="<{$env.app.res_url}>/style.css" media="screen" />
<div class="tableform">
  <div id="x-g-basic" class="goods-detail">
  <form action="index.php?app=ome&ctl=admin_consign&act=do_edit_logistics&finder_id=<{$env.get.finder_id}>" method="post" id="setting_frm" name="setting_frm">
      <input type="hidden" name="delivery_id" value="<{$delivery_id}>" />
<table>
    <tbody>
        <tr>
	        <td>原物流单号：</td>
	        <td>
                <label>
                        <input type="text" name="old_logistics_no" value="<{$logi_no}>"  readonly class="dailog-batch-ipt"/>
                </label>
            </td>
        </tr>
        <tr>
            <td>修改物流单号：</td>
	        <td>
                <label>
                    <input type="text" name="new_logistics_no" value="" class="dailog-batch-ipt"/>
                </label>
            </td>
        </tr>
        <tr>
            <td >
                <div class="table-action text_left" style="border:0px; text-align:center;">
                    <{button id="btn_setting" label="保存" type="submit" name="submit" }>
                    <{button label="关闭" type="button" id="btn-close"}>
                </div>
            </td>
        </tr>
    </tbody>
</table>
  </form>
  </div>
</div>
<script>
    (function(){
    var _form = $('setting_frm');
    var btn = $('btn_setting');//提交按钮
    var finder = finderGroup['<{$env.get.finder_id}>'];
    _form.store('target',{
        onComplete:function(){
        },
        onSuccess:function(response){

            var hash_res_obj = JSON.decode(response);

            if (hash_res_obj.success != undefined && hash_res_obj.success != ""){
                try{
                    var _dialogIns = btn.getParent('.dialog').retrieve('instance');
                }catch(e){}
                if(_dialogIns){
                    finder.refresh();
                    _dialogIns.close();
                }
            }
        }
    });

    btn.addEvent("click",function(e)
    {
        _form.fireEvent('submit',{stop:$empty});
    });

    $('btn-close').addEvent('click', function(){
        $('btn-close').getParent('.dialog').retrieve('instance').close();
    });
})();
</script>