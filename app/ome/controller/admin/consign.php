<?php

class ome_ctl_admin_consign extends desktop_controller{

    var $name = "发货处理";
    var $workground = "delivery_center";

    function _views() {

        $mdl_order = $this->app->model('orders');

        //未发货分两部分：sync=none+线上店铺 OR ship_status=0+线下店铺
        $shops = $this->app->model('shop')->getList('shop_id,node_id');
        $bindShop = $unbindShop = array();
        foreach ($shops as $key=>$shop) {
            if ($shop['node_id']) {
                $bindShop[] = $shop['shop_id'];
            } else {
                $unbindShop[] = $shop['shop_id'];
            }
        }
        $sync_none_filter = array('ship_status' => '0');
        if ($bindShop && $unbindShop) {
            $sync_none_filter['filter_sql'] = '(({table}sync="none" AND {table}shop_id in("'.implode('","',$bindShop).'"))'.' OR '.'({table}ship_status="0" AND shop_id in("'.implode('","',$unbindShop).'")))';
        } elseif ($bindShop) {
            $sync_none_filter['filter_sql'] = '{table}sync="none" AND {table}shop_id in("'.implode('","',$bindShop).'")';
        } elseif ($unbindShop) {
            $sync_none_filter['filter_sql'] = '{table}ship_status="0" AND {table}shop_id in("'.implode('","',$unbindShop).'")';
        }

        $base_filter = $this->getFilters();

        $sub_menu[0] = array('label' => app::get('base')->_('发货失败'), 'filter' => array_merge($base_filter, array('createway'=>'matrix','sync' => 'fail')), 'optional' => false,'addon' => '_FILTER_POINT_');
        //$sub_menu[1] = array('label' => app::get('base')->_('待发货'), 'filter' => array_merge($base_filter, $sync_none_filter), 'optional' => false);
        $sub_menu[2] = array('label' => app::get('base')->_('回写未发起'),'filter' => array_merge($base_filter,array('createway' => 'matrix','sync' => 'none' ,'ship_status' => '1')),'optional' => false);
        $sub_menu[3] = array('label' => app::get('base')->_('发货中'), 'filter' => array_merge($base_filter, array('createway'=>'matrix','sync' => 'run')), 'optional' => false);
        $sub_menu[4] = array('label' => app::get('base')->_('全部'), 'filter' => $base_filter, 'optional' => false);
        //$sub_menu[5] = array('label' => app::get('base')->_('京东发货失败'), 'filter' => array_merge($base_filter, array('createway'=>'matrix','sync' => 'fail','shop_type'=>'360buy')), 'optional' => false);
        //$sub_menu[6] = array('label' => app::get('base')->_('回写参数错误'), 'filter' => array_merge($base_filter, array('createway' => 'matrix','sync' => 'fail','sync_fail_type' => 'params')), 'optional' => false);
        //$sub_menu[7] = array('label' => app::get('base')->_('前端已发货'), 'filter' => array_merge($base_filter, array('createway' => 'matrix','sync' => 'fail','sync_fail_type' => 'shipped')), 'optional' => false);
        $sub_menu[8] = array('label' => app::get('base')->_('发货成功'), 'filter' => array_merge($base_filter, array('createway' => 'matrix','sync' => 'succ')), 'optional' => false);
        $sub_menu[9] = array('label' => app::get('base')->_('不予回写'),'filter' => array_merge($base_filter, array('createway' => array('local','import','after'))),'optional' => false);
        $sub_menu[10] = array('label' => app::get('base')->_('换货订单回写失败'),'filter' => array_merge($base_filter, array('createway' => array('after'),'sync'=>'fail')),'optional' => false);
        $sub_menu[11] = array('label' => app::get('base')->_('物流错误'), 'filter' => array_merge($base_filter, array('createway'=>'matrix', 'sync'=>'fail', 'sync_fail_type'=>'logistics')), 'optional'=>false);
        
        foreach ($sub_menu as $k => $v) {
            $sub_menu[$k]['filter'] = $v['filter'] ? $v['filter'] : null;
            $sub_menu[$k]['addon'] = $v['addon'] ? $v['addon'] : $mdl_order->viewcount($v['filter']);
            $sub_menu[$k]['href'] = 'index.php?app=ome&ctl=' . $_GET['ctl'] . '&act=' . $_GET['act'] . '&flt=' . $_GET['flt'] . '&view=' . $k . $s;
        }

        return $sub_menu;
    }
    
    /**
     * 极速发货
     * 
     * @param void
     * @return void
     */
    function fast_consign()
    {
        $_GET['view'] = intval($_GET['view']);
        $op_id = kernel::single('desktop_user')->get_id();
        switch ($_GET['view']) {
            case '0':
            case '2':
            case '3':
            case '5':
            case '6':
            case '7':
            case '11':
                $action = array(
                    array('label' => '批量发货', 'submit' => $this->url.'&act=dailog_delivery_confirm','target'=>'dialog::{width:800,height:200,title:\'批量发货\'}"'),

                    array('label' => '已回写成功', 'submit' => 'index.php?app=ome&ctl=admin_consign&act=batch_sync_succ', 'confirm' => "这些订单系统认为都是在前台(淘宝、京东等)已经发货，请确认这些订单前端已经发货！！！\n\n警告：本操作不会再同步发货状态，并不可恢复，请谨慎使用！！！", 'target' => 'refresh'),
                );
                break;
            case '9':
                $action = array(
                    array('label' => '批量发货', 'submit' => $this->url . 'index.php?app=ome&ctl=admin_consign&act=batch_change_sync', 'confirm' => '你确定要对勾选的订单进行发货操作吗？', 'target' => 'refresh'),

                    array('label' => '已回写成功', 'submit' => 'index.php?app=ome&ctl=admin_consign&act=batch_sync_succ', 'confirm' => "这些订单系统认为都是在前台(淘宝、京东等)已经发货，请确认这些订单前端已经发货！！！\n\n警告：本操作不会再同步发货状态，并不可恢复，请谨慎使用！！！", 'target' => 'refresh'),
                );
                break;
            default:
                break;
        }
        
        //修改物流
        if($_GET['view'] == '11'){
            $action[] = array(
                    'label' => '批量修改物流公司',
                    'submit' => $this->url.'&act=batch_edit_logistics',
                    'target' => 'dialog::{width:700,height:300,title:\'批量修改物流公司\'}"',
            );
//            $action[] = array(
//                    'label' => '修改物流单号',
//                    'submit' => $this->url.'&act=edit_logistics_no',
//                    'target' => 'dialog::{width:700,height:300,title:\'修改物流单号\'}"',
//            );
        }
        
        //params
        $params = array(
            'title' => '需发货订单',
            'actions' => $action,
            'use_buildin_new_dialog' => false,
            'use_buildin_set_tag' => false,
            'use_buildin_recycle' => false,
            'use_buildin_export' => true,
            'use_buildin_import' => false,
            'use_buildin_filter' => false,
            'use_view_tab' => true,
            'finder_aliasname' => 'order_consign_fast'.$op_id,
            'finder_cols' => '_func_0,column_confirm,order_bn,column_sync_status,column_print_status,logi_id,logi_no,column_deff_time,member_id, ship_name,ship_area,total_amount',
            'base_filter' => $this->getFilters(),
            'object_method' => [
                'count'   => 'finder_count',
                'getlist' => 'finder_getList',
            ]
        );
        $this->finder('ome_mdl_orders', $params);
    }
    
    /**
     * 批量消除冲突
     *
     * @param void
     * @return void
     */
    function batch_sync_succ() {
        $this->begin('');
        $ids = $_REQUEST['order_id'];

        if (!empty($ids)) {
            $orderObj = $this->app->model('orders');
            $data = array('sync'=>'succ','sync_fail_type'=>'none');
            $filter = array('order_id'=>$ids,'createway' => 'matrix');
            $orderObj->update($data,$filter);

            //记录日志
            $logObj = $this->app->model('operation_log');
            $logObj->batch_write_log('order_modify@ome',$filter,'手动设为同步成功',time());
        }
        $this->end(true, '命令已经被成功发送！');
    }

    /**
     * 批量发货
     *
     * @param void
     * @return void
     */
    function batch_sync() {

        $this->begin('');
        kernel::database()->exec('commit');
        $ids = $_REQUEST['order_id'];
        $deliveryObj = app::get('ome')->model('delivery');
        if (!empty($ids)) {

            kernel::single('ome_event_trigger_shop_delivery')->delivery_confirm_retry($ids);
        }
        $this->end(true, '命令已经被成功发送！！');
    }
    
    function getFilters()
    {
        $base_filter = array();
        $base_filter['status'] = array('active', 'finish');
        $base_filter['order_confirm_filter'] = "sdb_ome_orders.ship_status IN('1', '2') AND logi_no <> ''";
        
        //$base_filter['order_confirm_filter'] = "(sdb_ome_orders.op_id is not null OR sdb_ome_orders.group_id is not null ) AND (sdb_ome_orders.is_cod='true' OR sdb_ome_orders.pay_status='1' OR sdb_ome_orders.pay_status='4' OR sdb_ome_orders.pay_status='5') and logi_no <> ''";
        //$base_filter['process_status'] = array('splited', 'confirmed', 'splitting');
        
        /***
        //拆单配置_订单确认状态加入"余单撤消"
        $orderSplitLib    = kernel::single('ome_order_split');
        $split_seting     = $orderSplitLib->get_delivery_seting();
        if($split_seting){
            $base_filter['process_status'] = array('splited', 'confirmed', 'splitting', 'remain_cancel');
        }
        ***/
        
        //check shop permission
        $organization_permissions = kernel::single('desktop_user')->get_organization_permission();
        if($organization_permissions){
            $base_filter['org_id'] = $organization_permissions;
        }
        
        return $base_filter;
    }
    
    /**
     * 天猫换货订单 发货重试
     * @DateTime  2018-02-07T11:50:29+0800
     * @return
     */
    public function batch_change_sync(){

        $this->begin('');
        kernel::database()->exec('commit');
        $ids = $_REQUEST['order_id'];

        if (!empty($ids)) {

            foreach($ids as $order_id){
                kernel::single('ome_service_aftersale')->exchange_consigngoods($order_id);
            }
        }
        $this->end(true, '命令已经被成功发送！！');
    }

    public function dailog_delivery_confirm()
    {
        $_POST['sync'] = ['fail', 'run', 'none'];

        $orderMdl = app::get('ome')->model('orders');
        $order_list = $orderMdl->getList('order_id', $_POST);

        $order_id = array_column($order_list, 'order_id');
        
        $this->pagedata['GroupList']   = json_encode($order_id);

        $this->pagedata['request_url'] = $this->url . '&act=ajax_delivery_confirm';

        parent::dialog_batch();
    }
    
    /**
     * Ajax重试回传平台发货状态
     *
     * @return void
     */
    public function ajax_delivery_confirm()
    {
        $order_id = explode(',', $_POST['primary_id']);
        if (!$order_id) { echo 'Error: 请先选择订单';exit;}

        $retArr  = array(
            'itotal'    => count($order_id),
            'isucc'     => count($order_id),
            'ifail'     => 0,
            'err_msg'   => array(),
        );

        kernel::single('ome_event_trigger_shop_delivery')->delivery_confirm_retry($order_id);

        echo json_encode($retArr),'ok.';exit;
    }
    
    /**
     * 批量修改物流公司
     */
    public function batch_edit_logistics()
    {
        $orderObj = app::get('ome')->model('orders');
        
        $ids = $_POST['order_id'];
        
        //check
        if($_POST['isSelectedAll'] == '_ALL_'){
            die('不能使用全选功能,每次最多选择500条!');
        }
        
        if(count($ids) > 500){
            die('每次最多只能选择500条!');
        }
        
        //物流公司列表
        $sql = "SELECT corp_id,type,name FROM sdb_ome_dly_corp WHERE 1";
        $tempList = $orderObj->db->select($sql);
        
        $logiList = array();
        foreach ($tempList as $key => $val)
        {
            if(in_array($val['type'], array('o2o_pickup', 'o2o_ship'))){
                continue;
            }
            
            $logiList[] = $val;
        }
        
        //pagedata
        $this->pagedata['logiList'] = $logiList;
        $this->pagedata['GroupList'] = $ids;
        $this->pagedata['request_url'] = 'index.php?app=ome&ctl=admin_consign&act=ajaxEditLogistics';
        $this->pagedata['custom_html'] = $this->fetch('admin/order/edit_logistics.html');
        
        //调用desktop公用进度条
        parent::dialog_batch('ome_mdl_orders', false, 10, 10);
    }
    
    /**
     * 修改物流公司
     **/
    public function ajaxEditLogistics()
    {
        $orderObj = app::get('ome')->model('orders');
        $operLogObj = app::get('ome')->model('operation_log');
        
        //获取订单号
        parse_str($_POST['primary_id'], $postdata);
        if(!$postdata){
            echo 'Error: 请先选择订单';
            exit;
        }
        
        //物流公司ID
        $corp_id = $_POST['corp_id'];
        if(empty($corp_id)){
            echo 'Error: 请先选择物流公司';
            exit;
        }
        
        $retArr = array(
                'itotal'  => 0,
                'isucc'   => 0,
                'ifail'   => 0,
                'err_msg' => array(),
        );
        
        //物流公司列表
        $sql = "SELECT corp_id,type,name FROM sdb_ome_dly_corp WHERE corp_id=".$corp_id;
        $corpInfo = $orderObj->db->selectrow($sql);
        if(empty($corpInfo)){
            echo 'Error: 没有找到物流公司';
            exit;
        }
        
        //订单列表
        $list = $orderObj->getList('order_id,order_bn,ship_status,createway,sync', $postdata['f'], $postdata['f']['offset'], $postdata['f']['limit']);
        
        //count
        $retArr['itotal'] = count($list);
        
        foreach ((array)$list as $key => $val)
        {
            $order_id = $val['order_id'];
            
            //check
            if($val['ship_status'] != '1'){
                
                //error
                $retArr['ifail'] += 1;
                $retArr['err_msg'][] = $val['order_bn'].'订单不是已发货状态';
                
                continue;
            }
            
            if($val['createway'] != 'matrix'){
                
                //error
                $retArr['ifail'] += 1;
                $retArr['err_msg'][] = $val['order_bn'].'订单不是平台下来的';
                
                continue;
            }
            
            if($val['sync'] != 'fail'){
                
                //error
                $retArr['ifail'] += 1;
                $retArr['err_msg'][] = $val['order_bn'].'订单不是回传失败的状态';
                
                continue;
            }
            
            //关联发货单(只获取已发货的发货单)
            $sql = "SELECT b.delivery_id FROM sdb_ome_delivery_order AS a ";
            $sql .= " LEFT JOIN sdb_ome_delivery AS b ON a.delivery_id=b.delivery_id  WHERE a.order_id=". $order_id ." AND b.status='succ'";
            $deliveryList = $orderObj->db->select($sql);
            if(empty($deliveryList)){
                
                //error
                $retArr['ifail'] += 1;
                $retArr['err_msg'][] = $val['order_bn'].'没有找到已发货的发货单';
                
                continue;
            }

            $delivery_ids = array_column($deliveryList, 'delivery_id');
            if (count($delivery_ids) > 1){
                $retArr['ifail']     += 1;
                $retArr['err_msg'][] = $val['order_bn'] . '多个发货单不允许修改物流公司';
                continue;
            }
            
            //[发货单]更新物流公司
            $update_sql = "UPDATE sdb_ome_delivery SET logi_id='%d', logi_name='%s' WHERE delivery_id IN(". implode(',', $delivery_ids) .")";
            $update_sql = sprintf($update_sql, $corpInfo['corp_id'], $corpInfo['name']);
            $orderObj->db->exec($update_sql);
            
            //[订单]更新物流公司
            $update_sql = "UPDATE sdb_ome_orders SET logi_id='%d' WHERE order_id='%d'";
            $update_sql = sprintf($update_sql, $corpInfo['corp_id'], $order_id);
            $orderObj->db->exec($update_sql);
            
            //log
            $operLogObj->write_log('order_edit@ome', $order_id, '修改订单和发货单的物流公司为：'.$corpInfo['name']);
            
            //succ
            $retArr['isucc'] += 1;
        }
        
        echo json_encode($retArr),'ok.';
        exit;
    }


    /**
     * Notes: 修改物流单号
     * User: 七月
     * DateTime: 2025/3/28 10:13
     */
    public function edit_logistics_no(): void
    {
        //check
        if ($_POST['isSelectedAll'] == '_ALL_') {
            die('不能使用全选功能,每次最多选择1条!');
        }

        $ids = $_POST['order_id'];
        if (count($ids) > 1) {
            die('每次最多只能选择1条!');
        }

        $order_mdl = app::get('ome')->model('orders');
        $filter    = array(
            'order_id' => $ids,
        );
        $cols      = 'logi_no';
        $order     = $order_mdl->dump($filter, $cols);


        $this->pagedata['order']       = $order;
        $this->pagedata['GroupList']   = $ids;
        $this->pagedata['request_url'] = 'index.php?app=ome&ctl=admin_consign&act=ajaxEditLogisticsno';
        $this->pagedata['custom_html'] = $this->fetch('admin/order/edit_logistics_no.html');

        //调用desktop公用进度条
        parent::dialog_batch('ome_mdl_orders', false, 10, 10);
    }

    public function ajaxEditLogisticsno(): void
    {
        //获取订单号
        parse_str($_POST['primary_id'], $postdata);
        if (!$postdata) {
            echo 'Error: 请先选择订单';
            exit;
        }

        $new_logistics_no = trim($_POST['new_logistics_no']);
        $old_logistics_no = $_POST['old_logistics_no'];
        if (empty($new_logistics_no)) {
            echo 'Error: 请输入修改物流单号';
            exit;
        }

        if ($new_logistics_no == $old_logistics_no) {
            echo 'Error: 修改物流单号不能相同';
            exit;
        }

        $orderObj   = app::get('ome')->model('orders');
        $operLogObj = app::get('ome')->model('operation_log');
        //订单列表
        $list = $orderObj->getList('order_id,order_bn,ship_status,createway,sync', $postdata['f'], $postdata['f']['offset'], $postdata['f']['limit']);
        //count
        $retArr['itotal'] = count($list);
        foreach ((array)$list as $key => $val) {
            $order_id = $val['order_id'];
            //check
            if ($val['ship_status'] != '1') {

                //error
                $retArr['ifail']     += 1;
                $retArr['err_msg'][] = $val['order_bn'] . '订单不是已发货状态';

                continue;
            }

            if ($val['createway'] != 'matrix') {

                //error
                $retArr['ifail']     += 1;
                $retArr['err_msg'][] = $val['order_bn'] . '订单不是平台下来的';

                continue;
            }

            if ($val['sync'] != 'fail') {

                //error
                $retArr['ifail']     += 1;
                $retArr['err_msg'][] = $val['order_bn'] . '订单不是回传失败的状态';

                continue;
            }

            //关联发货单(只获取已发货的发货单)
            $sql          = "SELECT b.delivery_id FROM sdb_ome_delivery_order AS a LEFT JOIN sdb_ome_delivery AS b ON a.delivery_id=b.delivery_id  WHERE a.order_id=" . $order_id . " AND b.status='succ'";
            $deliveryList = $orderObj->db->select($sql);
            if (empty($deliveryList)) {
                //error
                $retArr['ifail']     += 1;
                $retArr['err_msg'][] = $val['order_bn'] . '没有找到已发货的发货单';
                continue;
            }

            $delivery_ids = array_column($deliveryList, 'delivery_id');
            //[发货单]更新物流单号
            $update_sql = "UPDATE sdb_ome_delivery SET logi_no='%s' WHERE delivery_id IN(" . implode(',', $delivery_ids) . ")";
            $update_sql = sprintf($update_sql, $new_logistics_no);
            $orderObj->db->exec($update_sql);

            $update_sql2 = "UPDATE sdb_ome_delivery_package SET logi_no='%s' WHERE delivery_id IN(" . implode(',', $delivery_ids) . ")";
            $update_sql2 = sprintf($update_sql2, $new_logistics_no);
            $orderObj->db->exec($update_sql2);

            $wap_delivery_sql = "SELECT a.delivery_id FROM sdb_wap_delivery AS a LEFT JOIN sdb_wap_delivery_bill AS b ON a.delivery_id=b.delivery_id  WHERE a.order_bn=" . $val['order_bn'] . " AND a.status='3'";
            $wapDeliveryList  = $orderObj->db->select($wap_delivery_sql);
            if (!empty($wapDeliveryList)) {
                $wap_delivery_ids = array_column($wapDeliveryList, 'delivery_id');
                //[门店发货单]更新物流单号
                $update_sql = "UPDATE sdb_wap_delivery_bill SET logi_no='%s' WHERE delivery_id IN(" . implode(',', $wap_delivery_ids) . ")";
                $update_sql = sprintf($update_sql, $new_logistics_no);
                $orderObj->db->exec($update_sql);
            }


            //[订单]更新物流单号
            $update_sql = "UPDATE sdb_ome_orders SET logi_no='%s' WHERE order_id='%d'";
            $update_sql = sprintf($update_sql, $new_logistics_no, $order_id);
            $orderObj->db->exec($update_sql);

            //log
            $operLogObj->write_log('order_edit@ome', $order_id, '修改订单和发货单的物流单号：' . $new_logistics_no . '(原物流单号：' . $old_logistics_no . ')');
            //succ
            $retArr['isucc'] += 1;
        }

        echo json_encode($retArr), 'ok.';
        exit;
    }


    public function edit_logistics($delivery_id, $logi_no): void
    {
        $this->pagedata['logi_no']     = $logi_no;
        $this->pagedata['delivery_id'] = $delivery_id;
        $this->page('admin/order/edit_logistics_no.html');
    }

    public function do_edit_logistics(): void
    {
        $this->begin('javascript:finderGroup["' . $_GET['finder_id'] . '"].refresh();');
        $new_logistics_no = trim($_POST['new_logistics_no']);
        $old_logistics_no = $_POST['old_logistics_no'];
        if (empty($new_logistics_no)) {
            $this->end(false, '请输入修改物流单号');
        }
        if ($new_logistics_no == $old_logistics_no) {
            $this->end(false, '修改物流单号不能相同');
        }
        $delivery_id          = $_POST['delivery_id'];
        $delivery_mdl         = app::get('ome')->model('delivery');
        $delivery_package_mdl = app::get('ome')->model('delivery_package');
        $wap_delivery_mdl     = app::get('wap')->model('delivery');
        $delivery             = $delivery_mdl->dump($delivery_id);
        if (empty($delivery)) {
            $this->end(false, '发货单不存在');
        }

        if ($delivery['status'] != 'succ') {
            $this->end(false, '已发货发货单才允许修改物流单号');
        }

        // if(time() - $delivery['delivery_time'] > 60 * 60 * 24 * 3){
        //     $this->end(false, '发货时间超过3天不允许修改物流单号');
        // }

        //更新条件精确到物流单号，兼容多包裹的情况
        $ome_delivery_filter = ['delivery_id' => $delivery_id, 'logi_no' => $old_logistics_no];
        $update_data         = ['logi_no' => $new_logistics_no];
        $r                   = $delivery_mdl->update($update_data, $ome_delivery_filter);
        if (!$r) {
            $this->end(false, '发货单物流单号修改失败');
        }
        $r = $delivery_package_mdl->update($update_data, $ome_delivery_filter);
        if (!$r) {
            $this->end(false, '发货单物流包裹明细物流单号修改失败');
        }
        $wap_delivery_filter = ['order_bn' => $delivery['platform_order_bn']];
        $wap_delivery_list   = $wap_delivery_mdl->getList('delivery_id', $wap_delivery_filter);
        if ($wap_delivery_list) {
            $wap_delivery_ids      = array_column($wap_delivery_list, 'delivery_id');
            $wap_delivery_bill_mdl = app::get('wap')->model('delivery_bill');
            $sql                   = "UPDATE sdb_wap_delivery_bill SET logi_no='%s' WHERE delivery_id IN('%s') AND logi_no='%s'";
            $sql                   = sprintf($sql, $new_logistics_no, implode("','", $wap_delivery_ids), $old_logistics_no);
            $r                     = $wap_delivery_bill_mdl->db->exec($sql);
            if (!$r) {
                $this->end(false, '门店发货单物流单号修改失败');
            }
        }
        $operLogObj = app::get('ome')->model('operation_log');
        //log
        $operLogObj->write_log('delivery_modify@ome', $delivery_id, '修改发货单的物流单号：' . $new_logistics_no . '(原物流单号：' . $old_logistics_no . ')');
        //物流订阅
        kernel::single('ome_event_trigger_shop_hqepay')->hqepay_pub($delivery_id);
        //发货回传
        if ($delivery['platform_order_bn']) {
            $order_mdl    = app::get('ome')->model('orders');
            $order_filter = ['order_bn' => $delivery['platform_order_bn']];
            $order_info   = $order_mdl->dump($order_filter, 'order_id,ship_status,sync');
            if ($order_info['ship_status'] == '1' && $order_info['sync'] != 'succ') {
                kernel::single('ome_event_trigger_shop_delivery')->delivery_confirm_retry($order_info['order_id']);
            }
        }
        $this->end(true, '修改成功');

    }
}
