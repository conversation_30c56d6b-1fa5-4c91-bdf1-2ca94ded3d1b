<?php
class ome_ctl_admin_return extends desktop_controller
{
    public $name       = "售后服务";
    public $workground = "aftersale_center";

    public function index($is_jingxiao = false)
    {

        $action = array(array(
            'label'  => '新建售后服务',
            'href'   => 'index.php?app=ome&ctl=admin_return&act=add_return',
        ));
        /*
         * 没维护 故弃用
         $action[] = array(
            'label'  => '单拉天猫换货单',
            'href'   => 'index.php?app=ome&ctl=admin_return&act=sync_aftersale',
            'target' => "dialog::{width:500,height:200,title:'同步换货单'}",
        );*/
        switch (intval($_GET['view'])) {
            case '0':
            case '1':
            case '2':
//                $action[] = array(
//                    'label'  => '批量同意退货',
//                    'submit' => 'index.php?app=ome&ctl=admin_return&act=batch_syncUpdate&status_type=agree',
//                    'target' => "dialog::{width:700,height:490,title:'批量同意退货'}",
//                );
                break;
            default:
                break;
        }
        $base_filter = array('is_fail' => 'false');

        //check shop permission
        $organization_permissions = kernel::single('desktop_user')->get_organization_permission();
        if ($organization_permissions) {
            $base_filter['org_id'] = $organization_permissions;
        }

        $action[] = array(
            'label'  => '人工拦截',
            'submit' => 'index.php?app=ome&ctl=admin_return&act=hand_interception',
            'target' => "dialog::{width:700,height:490,title:'人工拦截'}",
        );

        $params = array(
            'title'                  => '售后服务',
            'use_buildin_new_dialog' => false,
            'use_buildin_set_tag'    => false,
            'use_buildin_recycle'    => false,
            'use_buildin_export'     => true,
            'use_buildin_import'     => false,
            'use_buildin_filter'     => true,
            'actions'                => $action,
            'base_filter'            => $base_filter,
        );

        # 权限判定
        if (!$this->user->is_super()) {
            $returnLib = kernel::single('ome_return');
            foreach ($params['actions'] as $key => $action) {
                $url = parse_url($action['href']);
                parse_str($url['query'], $url_params);
                $has_permission = $returnLib->chkground($this->workground, $url_params);
                if (!$has_permission) {
                    unset($params['actions'][$key]);
                }
            }
        }

        if ($is_jingxiao) {
            $params['actions'] = [];
            $params['base_filter']['delivery_mode'] = 'jingxiao';
            $params['use_buildin_export'] = false;
            $params['title'] = '平台自发售后服务';
        } else {
            if (!$params['base_filter']['filter_sql']) {
                $params['base_filter']['filter_sql'] = '1';
            }
            $params['base_filter']['filter_sql'] .= ' AND (delivery_mode <> "jingxiao" OR delivery_mode IS NULL)';
        }

        $params['orderBy'] = 'add_time DESC';
        //售后申请表
        $this->finder('ome_mdl_return_product', $params);
    }

    public function jingxiao()
    {
        $is_jingxiao = true;
        return $this->index($is_jingxiao);
    }

    public function _views()
    {
        if ($_GET['act'] == 'return_io') {
            return [];
        }

        $mdl_return_product = $this->app->model('return_product');
        $sub_menu           = array(
            0 => array('label' => __('全部'), 'optional' => false, 'filter' => array('is_fail' => 'false')),
            1 => array('label' => __('未处理'), 'filter' => array('status' => '1', 'is_fail' => 'false', 'belong_type'=>'customer','wait_customer_check'=>'1'), 'optional' => false),
            2 => array('label' => __('审核中'), 'filter' => array('status' => '2', 'is_fail' => 'false'), 'optional' => false),
            3 => array('label' => __('接受申请'), 'filter' => array('status' => '3', 'is_fail' => 'false'), 'optional' => false),
            4 => array('label' => __('完成'), 'filter' => array('status' => '4', 'is_fail' => 'false'), 'optional' => false),
            5 => array('label' => __('拒绝'), 'filter' => array('status' => '5', 'is_fail' => 'false'), 'optional' => false),
            6 => array('label' => __('已收货'), 'filter' => array('status' => '6', 'is_fail' => 'false'), 'optional' => false),
            7 => array('label' => __('已质检'), 'filter' => array('status' => '7', 'is_fail' => 'false'), 'optional' => false),
            8 => array('label' => __('补差价'), 'filter' => array('status' => '8', 'is_fail' => 'false'), 'optional' => false),
            9 => array('label' => __('已拒绝退款'), 'filter' => array('status' => '9', 'is_fail' => 'false'), 'optional' => false),
            10 => array('label' => __('卖家拒绝退款'), 'filter' => array('platform_status'=>'SELLER_REFUSE_BUYER'), 'optional' => false),
        );

        //check shop permission
        $organization_permissions = kernel::single('desktop_user')->get_organization_permission();

        $act = 'index';
        $i = 0;
        foreach ($sub_menu as $k => $v) {
            if ($organization_permissions) {
                $v['filter']['org_id'] = $organization_permissions;
            }

            if ($_GET['act'] == 'jingxiao') {
                $act = $_GET['act'];
                $v['filter']['delivery_mode'] = $act;
            } else {
                if (!$v['filter']['filter_sql']) {
                    $v['filter']['filter_sql'] = '1';
                }
                $v['filter']['filter_sql'] .= ' AND (delivery_mode <> "jingxiao" OR delivery_mode IS NULL)';
            }

            $sub_menu[$k]['filter'] = $v['filter'] ? $v['filter'] : null;
            $sub_menu[$k]['addon']  = $mdl_return_product->count($v['filter']);
            $sub_menu[$k]['href']   = 'index.php?app=ome&ctl=admin_return&act=' . $act . '&view=' . $i++;
        }
        return $sub_menu;
    }

    //编辑弹窗触发
    public function edit($return_id)
    {
        if (!intval($return_id)) {
            die('单据号传入错误');
        }

        $return_info = $this->app->model('return_product')->dump(array('return_id' => $return_id), 'order_id');
        $this->apply($return_info['order_id'], $return_id, 'edit');
    }

    /*
     * 对当前售后申请各状态进行保存
     * @param Int $status 申请售后状态
     */
    public function save($status)
    {
        $oProduct           = $this->app->model('return_product');
        $oOrder             = $this->app->model('orders');
        $_POST['return_id'] = (int) $_POST['return_id'];
        
        $this->begin();
        
        $return_id   = $_POST['return_id'];
        $address_id = intval($_POST['address_id']); //平台退货地址ID
        
        $bn_array    = array();
        $oPro_detail = $oProduct->dump($return_id, 'status,order_id,shop_id,source,attachment,shop_type');
        
        //如果是本地订单不需要根据平台去读取扩展
        $source     = $oPro_detail['source'];
        $archiveLib = kernel::single('archive_order');
        if ($archiveLib->is_archive($source)) {
            $archive_ordObj = kernel::single('archive_interface_orders');
            $order_detail   = $archive_ordObj->getOrders(array('order_id' => $oPro_detail['order_id']), 'ship_status,shop_type');
        } else {
            $order_detail = $oOrder->dump($oPro_detail['order_id'], 'ship_status,shop_type');
        }
        if ($status != '5') {
            //当提交状态为拒绝时不判断 判断订单是否已经全部退货
            if ($order_detail['ship_status'] == '4') {
                $this->end(false, app::get('base')->_('订单已全部退货,请拒绝此售后申请!'));
            }
        }
        $_POST['shop_id'] = $oPro_detail['shop_id'];
        
        if ($status == 2 || $status == 3) {
            $adata    = $_POST;
            
            //平台退货地址ID
            if($address_id){
                $adata['address_id'] = $address_id;
            }
            
            //售后---审核出现两次,begin
            $now_status = $oProduct->getList('status,delivery_id', array('return_id' => $return_id));
            if (empty($now_status[0]['delivery_id'])) {
                $this->end(false, app::get('base')->_('收货地址未选择，请先进入编辑界面选择'));
            }
            if ($now_status[0]['status'] != 3) {
                //预先做判断看是否可以生成
                $choose_type = $adata['choose_type'];
                if (in_array($choose_type, array('1', '2'))) {
                    $choose_flag    = true;
                    $Oproduct_items = $this->app->model('return_product_items');
                    $reship_items   = $this->app->model('reship_items');
                    $pro_items      = $Oproduct_items->getList('*', array('return_id' => $return_id, 'disabled' => 'false'));
                    foreach ($pro_items as $k => $v) {
                        $apply_num = $v['num'];
                        $bn        = $v['bn'];
                        if (in_array($source, array('archive'))) {
                            $effective = $archive_ordObj->Get_refund_count($oPro_detail['order_id'], $bn);
                        } else {
                            $effective = $reship_items->Get_refund_count($oPro_detail['order_id'], $bn, '', $v['order_item_id']);
                        }
                        if ($effective <= 0) {
                            $choose_flag = false;
                            break;
                        }
                    }
                    if (!$choose_flag) {
                        $this->end(false, app::get('base')->_('货品剩余数量不足,不可以申请!'));
                    }
                }
                
                //更新售后申请单,并且创建退换货单
                $error_msg = '';
                $result = $oProduct->tosave($adata, true, $error_msg);
                if($result === false){
                    $this->end(false, app::get('base')->_('售后申请单接受申请失败('. $error_msg .')!'));
                }
                
                //更新退换货单上最后合计金额totalmoney
                if ($adata['status'] == 3 && $adata['choose_type_flag']) {
                    kernel::single('ome_return_rchange')->update_totalmoney($return_id);
                }
            }
            //售后---审核出现两次,end
        } else {
            if ($status == 5 && $order_detail['shop_type'] == 'youzan') {
                $this->end(false, app::get('base')->_('有赞平台商家拒绝退货，仅支持 “买家已经退货，等待卖家确认收货 ”的场景'));
            }
            
            //[京东一件代发]抖单平台需要拒绝图片
            if($status == 5 && $order_detail['shop_type']=='luban')
            {
                $fileObj = app::get('base')->model('files');
                
                //获取拒绝退货图片(抖单平台需要拒绝图片)
                $file_url = '';
                if(is_numeric($oPro_detail['attachment']))
                {
                    $fileLib = kernel::single('base_storager');
                    $file_url = $fileLib->getUrl($oPro_detail['attachment']);
                }
                else
                {
                    $tempData = explode('|', $oPro_detail['attachment']);
                    $file_url = $tempData[0];
                }
                
                //没有上传退货图片,不允许拒绝退货
                if(empty($file_url))
                {
                    $this->end(false, app::get('base')->_('抖单平台需要拒绝图片，请先编辑单据，上传拒绝图片!'));
                }
            }
            
            $data = array('return_id' => $return_id, 'status' => $_POST['status'], 'last_modified' => time());
            
            //平台退货地址ID
            if($address_id){
                $data['address_id'] = $address_id;
            }
            
            $oProduct->save($data);
            $memo = $oProduct->schema['columns']['status']['type'][$_POST['status']];
            $this->app->model('operation_log')->write_log('return@ome', $return_id, '售后服务:' . $memo);
        }
        //平台对保存前的扩展
        if ($source == 'matrix') {
            $pre_result = kernel::single('ome_aftersale_service')->pre_save_return($_POST);
            if ($pre_result['rsp'] == 'fail') {
                $this->end(false, $pre_result['msg']);
            }
        }
        $this->end(true, app::get('base')->_('操作成功'));
    }

    //新建售后服务弹出页
    public function add_return()
    {
        $source                          = trim($_GET['source']);
        $this->pagedata['source']        = $source;
        $this->pagedata['search_filter'] = array('ship_name' => '收货人', 'ship_tel' => '收货人电话', 'ship_mobile' => '收货人手机', 'order_bn' => '订单号');
        $this->display("admin/return_product/add_return.html");
    }

    //新增或者编辑售后服务申请 弹窗展示
    public function apply($order_id, $return_id = '', $act = 'add')
    {
        $basicMaterialExtObj = app::get('material')->model('basic_material_ext');
        $archiveLib          = kernel::single('archive_order');
        $archive_ordObj      = kernel::single('archive_interface_orders');
        $order_id            = intval($order_id);
        if (!$order_id) {
            die('订单传入错误');
        }
        
        $oReship_item = $this->app->model('reship_items');
        $oProduct     = $this->app->model('return_product');
        
        $product    = $oProduct->dump(array('return_id' => $return_id));
        
        $source = trim($_GET['source']);
        
        //是否归档售后单
        if(empty($source)){
            $source = ($product['archive'] ? 'archive' : '');
        }
        
        if (!is_numeric($product['attachment'])) {
            $this->pagedata['attachment_type'] = 'remote';
            $attachment                        = explode("|", $product['attachment']);
            if ($attachment[0] != '') {
                $product['attachment'] = $attachment;
            }
        }
        
        $archiveLib = kernel::single('archive_order');
        if (($source && $archiveLib->is_archive($source)) || ($product && $archiveLib->is_archive($product['source']))) {
            $order_items = $archive_ordObj->getItemList($order_id);
            $items       = $archive_ordObj->getItemList($order_id);
        } else {
            $oOrders_item = $this->app->model('order_items');
            $order_items  = $oOrders_item->getList('*', array('order_id' => $order_id, 'delete' => 'false'));
            $items        = $oOrders_item->getList('*', array('order_id' => $order_id));
        }
        $newItems = array();
        if ($act == 'edit') {
            //编辑展示
            $oProduct_items = $this->app->model('return_product_items');
            $items          = $oProduct_items->getList('*', array('return_id' => $return_id));
            $sendnum_list   = array();
            foreach ($order_items as $oitems) {
                $sendnum_list[$oitems['bn']] = $oitems;
            }
            foreach ($items as $k => $v) {
                $spec_info              = $basicMaterialExtObj->dump(array('bm_id' => $v['product_id']), '*');
                $items[$k]['spec_info'] = $spec_info['specifications'];
                //if($newItems[$v['bn']] && $newItems[$v['bn']]['bn'] !=''){
                //$newItems[$v['bn']]['nums'] += $items[$k]['nums'];
                // $newItems[$v['bn']]['sendnum'] += $items[$k]['sendnum'];
                //$newItems[$v['bn']]['price'] = $order_items[$k]['price'];
                //$newItems[$v['bn']]['sale_price'] = $order_items[$k]['sale_price'];
                //}else{
                if (in_array($product['source'], array('archive'))) {
                    $refund              = $archive_ordObj->Get_refund_count($order_id, $v['bn']);
                    $items[$k]['branch'] = $archive_ordObj->getBranchCodeByBnAndOd($v['bn'], $order_id);
                } else {
                    $refund              = $oReship_item->Get_refund_count($order_id, $v['bn'], '', $v['order_item_id']);
                    $items[$k]['branch'] = $oReship_item->getBranchCodeByBnAndOd($v['bn'], $order_id);
                }
                $items[$k]['effective']  = $refund;
                $items[$k]['sendnum']    = $sendnum_list[$v['bn']]['sendnum'];
                $items[$k]['price']      = $sendnum_list[$v['bn']]['price'];
                $items[$k]['sale_price'] = $sendnum_list[$v['bn']]['sale_price'];
                $items[$k]['nums']       = $sendnum_list[$v['bn']]['nums'];
                $newItems[]              = $items[$k];
                // }
            }
            #扩展页面
            $plugin_html = kernel::single('ome_aftersale_service')->pre_return_product_edit($product);
            if ($plugin_html && isset($plugin_html['rsp'])) {
                if($plugin_html['rsp'] != 'fail'){
                    $this->pagedata['plugin_html_show'] = $plugin_html;
                }
            }
        } else {
            //新增展示
            foreach ($items as $k => $v) {
                $spec_info              = $basicMaterialExtObj->dump(array('bm_id' => $v['product_id']), '*');
                $items[$k]['spec_info'] = $spec_info['specifications'];
                /*去除订单明细ID*/

                //if($newItems[$v['bn']] && $newItems[$v['bn']]['bn'] !=''){
                //$newItems[$v['bn']]['nums'] += $items[$k]['nums'];
                //$newItems[$v['bn']]['sendnum'] += $items[$k]['sendnum'];
                //}else{
                if ($source && in_array($source, array('archive'))) {
                    $refund              = $archive_ordObj->Get_refund_count($order_id, $v['bn']);
                    $items[$k]['branch'] = $archive_ordObj->getBranchCodeByBnAndOd($v['bn'], $order_id);
                } else {
                    $refund              = $oReship_item->Get_refund_count($order_id, $v['bn'], '', $v['item_id']);
                    $items[$k]['branch'] = $oReship_item->getBranchCodeByBnAndOd($v['bn'], $order_id);
                }
                $items[$k]['effective']     = $refund;
                $items[$k]['order_item_id'] = $v['item_id'];
                unset($items[$k]['item_id']);
                $newItems[] = $items[$k];
                //}
                unset($v['item_id']);
            }
        }
        $items = $newItems;

        //获取仓库模式
        $branch_mode                   = app::get('ome')->getConf('ome.branch.mode');
        $this->pagedata['branch_mode'] = $branch_mode;
        $this->pagedata['items']       = $items;
        $this->pagedata['order_id']    = $order_id;
        $this->pagedata['act']         = $act;
        $this->pagedata['finder_id']   = $_GET['finder_id'] ? $_GET['finder_id'] : $_POST['finder_id'];

        if ($product['delivery_id']) {
            if (in_array($product['source'], array('archive'))) {
                $archive_delObj = kernel::single('archive_interface_delivery');
                $deli_info      = $archive_delObj->getDelivery(array('delivery_id' => $product['delivery_id']), 'delivery_bn,ship_name,ship_area,ship_addr');
            } else {
                $Odelivery = $this->app->model('delivery');
                $deli_info = $Odelivery->dump(array('delivery_id' => $product['delivery_id']), 'delivery_bn,ship_name,ship_area,ship_addr,shop_type');
            }
            list($package, $region_name, $region_id) = explode(':', $deli_info['consignee']['area']);
            if ($region_name) {
                $deli_info['consignee']['area'] = str_replace('/', '-', $region_name);
            }
            $product = array_merge($product, $deli_info);
        }

        $this->pagedata['source']  = $source;
        $this->pagedata['product'] = $product;
        $this->display("admin/return_product/return_apply.html");
    }

    //售后申请执行新增、编辑操作
    public function apply_add()
    {
        $this->begin('index.php?app=ome&ctl=admin_return&act=index');
        $url         = $_POST['url'];
        $oProduct    = $this->app->model('return_product');
        $oItems      = $this->app->model('return_product_items');
        $oOrder      = $this->app->model('orders');
        $oOrder_item = $this->app->model('order_items');

        $order_id       = $_POST['order_id'];
        $order          = $oOrder->dump($order_id, 'member_id,shop_id,org_id');
        $oShop          = $this->app->model('shop');
        $shop_info      = $oShop->getShopInfo($order['shop_id']);
        $shop_type      = $shop_info['shop_type'];
        $source         = $_POST['source'];
        
        //归档订单加载类
        if ($source && in_array($source,array('archive'))) {
            $oOrder = app::get('archive')->model('orders');
            $oOrder_item = app::get('archive')->model('order_items');
        }
        
        $delivery_id    = $_POST['delivery_id'];
        $title          = $_POST['title'];
        $archive_ordObj = kernel::single('archive_interface_orders');
        if ($title == '') {
            $this->end(false, '售后标题不可为空!');
        } else if (empty($_POST['goods_bn'])) {
            $this->end(false, '您没有选择商品!');
        } else if ($delivery_id == '') {
            $this->end(false, '请为此售后服务选择收货人信息!');
        }

        $apply_money = 0;
        $items       = array();
        $storeList = [];
        foreach ($_POST['goods_bn'] as $key => $val) {

            $product_id = intval($_POST['product_id'][$val]);

            //获取订单货品信息
            $itemInfo = $oOrder_item->dump(array('order_id' => $_POST['order_id'], 'product_id' => $product_id, 'item_id' => $val), 'bn,price,sale_price,nums,divide_order_fee,part_mjz_discount');
            if (empty($itemInfo)) {
                $this->end(false, '货号：' . $val . ' 在订单明细中没有找到!');
            }

            //查找基础物料所属门店
            $storeInfo = kernel::single("o2o_store_material")->getOrderStore($itemInfo['bn'],$_POST['order_id']);
            if(!in_array($storeInfo['store_bn'], $storeList)){
                $storeList[] = $storeInfo['store_bn'];
            }
            if(count($storeList) > 1){
                $this->end(false, "售后申请不允许同时申请不同门店的商品");
            }

            //订单有实付金额时,货品退款金额使用实付金额
            if (($itemInfo['divide_order_fee'] && $itemInfo['divide_order_fee'] > 0) || $itemInfo['part_mjz_discount'] > 0) {
                $sale_price = $itemInfo['divide_order_fee']; //实付金额
            } else {
                $sale_price = $itemInfo['sale_price']; //货品销售总价
            }

            $item_num = ($itemInfo['quantity'] ? $itemInfo['quantity'] : $itemInfo['nums']);//货品销售数量(归档是nums)

            //售后申请数量及价格
            $apply_num   = intval($_POST['num'][$val]); //售后申请数量
            $apply_price = $sale_price / $item_num; //申请退换货单价 = 货品销售总价 / 货品销售数量;

            //售后合计申请金额
            $apply_money += ($apply_price * $apply_num);

            $item               = array();
            $item['bn']         = $_POST['bn'][$val];
            $item['item_id']    = $_POST['item_id'][$val];
            $item['name']       = $_POST['goods_name'][$val];
            $item['product_id'] = $product_id;

            $item['num']   = $apply_num;
            $item['price'] = $apply_price;

            $_POST['effective'][$val] = intval($_POST['effective'][$val]);

            if ($apply_num <= 0) {
                $this->end(false, '申请数量不可以为小于1的整数!');
            }
            if ($_POST['effective'][$val] <= 0) {
                $this->end(false, '剩余数量不足，不可以操作!');

            } else if ($apply_num > $_POST['effective'][$val]) {
                $this->end(false, '申请数量大于剩余数量，不可以操作!');
            }

            $val       = str_replace(" ", "_", $val);
            $branch_id = $_POST['branch_id' . $val]; //fix by danny 2012-5-18
            if (empty($branch_id)) {
                $this->end(false, '货品仓库不能为空!');
            }
            if ($source && in_array($source, array('archive'))) {
                $branch_num = $archive_ordObj->Get_delivery($branch_id, $val, $order_id);
            } else {
                $branch_num = $oProduct->Get_delivery($branch_id, $val, $order_id);
            }

            if ($apply_num > $branch_num) {
                $this->end(false, '所选仓库数量不足!');
            }

            $item['branch_id']     = $branch_id;
            $item['order_item_id'] = $val;
            $items[]               = $item;
        }
        $upload_file = "";
        if ($_FILES['attachment']['size'] != 0) {
            if ($_FILES['attachment']['size'] > 314572800) {
                $this->end(false, '上传文件不能超过300M!');
            }
            $type = array("jpg", "gif", "bmp", "jpeg", "rar", "zip");
            if ($_FILES['attachment']['name']) {
                if (!in_array(strtolower(substr(strrchr($_FILES['attachment']['name'], '.'), 1)), $type)) {
                    $text = implode(",", $type);
                    $this->end(false, "您只能上传以下类型文件{$text}!");
                }
            }

            $ss                  = kernel::single('base_storager');
            $id                  = $ss->save_upload($_FILES['attachment'], "file", "", $msg); //返回file_id;
            $upload_file         = $id;
            $aData['attachment'] = $upload_file;
        }
        $aData['order_id'] = $order_id;

        //合计申请金额
        $aData['money'] = $apply_money;

        $aData['title']     = $_POST['title'];
        $aData['add_time']  = time();
        $aData['member_id'] = $order['member_id']; //申请人
        $aData['content']   = $_POST['content'];
        $aData['memo']      = $_POST['memo'];
        $aData['status']    = 1;
        $aData['shop_id']   = $order['shop_id']; //店铺id
        $aData['shop_type'] = $shop_type; //店铺类型
        $opInfo             = kernel::single('ome_func')->getDesktopUser();
        $aData['op_id']     = $opInfo['op_id'];
        if ($source && in_array($source, array('archive'))) {
            $aData['source']  = $source;
            $aData['archive'] = '1';
        }
        $aData['delivery_id'] = $_POST['delivery_id'];

        // 经销店铺的单据，delivery_mode冗余到售后申请表
        if ($shop_info['delivery_mode'] == 'jingxiao') {
            $aData['delivery_mode'] = $shop_info['delivery_mode'];
        }

        if ($_POST['return_id']) {
            $aData['return_bn'] = $_POST['return_bn'];
            $aData['return_id'] = $_POST['return_id'];
            $add_operation      = '修改';
            $method             = 'update_status';
        } else {
            $return_bn          = $oProduct->gen_id();
            $aData['return_bn'] = $return_bn;
            $aData['org_id']    = $order['org_id'];
            $add_operation      = '创建';
            $method             = 'add_aftersale';
        }


        //如果是退货退款 并且是门店履约 判断是否7天无理由
        if (isset($storeInfo['performance_type']) && $storeInfo['performance_type'] == 'store' && !$_POST['return_id']) {
            $aData['belong_store_id'] = isset($storeInfo['store_id']) ? $storeInfo['store_id'] : 0;
            $aData['belong_store_bn'] = isset($storeInfo['store_bn']) ? $storeInfo['store_bn'] : '';

            if($shop_type == 'wxshipin'){
                $seven_no_reason_content = ['不想要了','尺码/颜色/款式不合适'];
            }else{
                $seven_no_reason_content = ['多拍/错拍/不想要','不喜欢/效果不好','7天无理由退款','不想要了','尺码不合适','大小/尺寸不合适','大小/颜色/型号等不合适'];
            }

            if (in_array($aData['content'], $seven_no_reason_content)) {
                $aData['belong_type'] = 'store';
            } else {
                //非7天无理由，获取指定渠道，需要客服审核
                $shopIds = app::get('ome')->getConf('seven.return.shop');
                $shopIds = $shopIds ?: [];
                if (in_array($aData['shop_id'], $shopIds)) {
                    $aData['belong_type'] = 'customer';
                    $aData['twentyfour_hours_check'] = '1';
                } else {
                    $aData['belong_type'] = 'store';
                }
            }
        }

        $oProduct->save($aData);
        $oItems->update(array('disabled' => 'true'), array('return_id' => $aData['return_id']));

        foreach ($items as $k => $v) {
            $v['return_id'] = $aData['return_id'];
            $v['disabled']  = 'false';
            $oItems->save($v);
        }

        //日志记录
        $oOperation_log = $this->app->model('operation_log');
        $memo           = $add_operation . '售后服务';
        $oOperation_log->write_log('return@ome', $aData['return_id'], $memo);

        //售后申请 API
        foreach (kernel::servicelist('service.aftersale') as $object => $instance) {
            if (method_exists($instance, $method)) {
                $instance->$method($aData['return_id']);
            }
        }

        #售后操作
        kernel::single('ome_aftersale_service')->return_product_edit_after($_POST);

        $this->end(true, "售后服务{$add_operation}成功!");
    }

    //ajax 根据仓库ID，货号订单号获取发货单号以及对应收货相关信息
    public function check()
    {
        $branch_id = $_GET['branch_id'];
        $bn        = $_GET['bn'];
        $order_id  = $_GET['order_id'];
        $source    = $_GET['source'];
        if ($source && in_array($source, array('archive'))) {
            $archive_ordObj = kernel::single('archive_interface_orders');
            $result         = $archive_ordObj->Get_delivery($branch_id, $bn, $order_id);
        } else {
            $oProduct = $this->app->model('return_product');
            $result   = $oProduct->Get_delivery($branch_id, $bn, $order_id);
        }
        $result['ship_addr'] = kernel::single('base_view_helper')->modifier_cut($result['ship_addr'],-1,'*',false,true);
        $result['ship_name'] = kernel::single('base_view_helper')->modifier_cut($result['ship_name'],-1,'*',false,true);
    
        echo json_encode($result);
    }

    //文件下载
    public function file_download2($return_id)
    {
        $oProduct = $this->app->model('return_product');
        $info     = $oProduct->dump($return_id);
        $filename = $info['attachment'];
        if (is_numeric($filename)) {
            $ss = kernel::single('base_storager');
            $a  = $ss->getUrl($filename, "file");
            $oProduct->file_download($a);
        } else {
            header('Location:' . $filename);
        }
    }

    /**
     * 展开明细 接受申请 选择操作类型
     * 1.退货单，2.换货单，3.退款申请单
     **/
    public function choose_type($return_id, $status)
    {
        if (!$return_id) {
            die("单据号传递错误！");
        }

        //根据类型转化是否继续，否则保存当前状态
        $Oreturn = $this->app->model('return_product');
        $reship = $Oreturn->dump(array('return_id' => $return_id), '*');
        $shop_id = $reship['shop_id'];
        $return_type = $reship['return_type'];
        
        $choose_type_flag  = 0;
        $choose_type_value = 0;
        if ($reship['source'] == 'matrix') {
            $router = kernel::single('ome_aftersale_request');
            if (!$router->setShopId($shop_id)->choose_type()) {
                $choose_type_flag = 1;
            }
            $choose_type_value = $router->setShopId($shop_id)->choose_type_value($return_id);
        }
        
        //判断类型
        $bbcObj = app::get('ome')->model('return_product_bbc');

        $bbc_detail  = $bbcObj->dump(array('return_bn' => $reship['return_bn'], 'shop_id' => $shop_id), 'return_type');
        if($bbc_detail){
            $return_type = $bbc_detail['return_type'];
        }
        
        // 根据天猫类型来定义
        $tmall_detail = kernel::single('ome_service_aftersale')->get_return_type($reship);

        if ($tmall_detail) {
            $return_type = $tmall_detail['refund_type'];
        }
        
        //获取退货地址列表
        $addressObj = app::get('ome')->model('return_address');
        $addressList = $addressObj->getList('*', array('shop_id'=>$shop_id, 'reship_id'=>0), 0, 100, 'cancel_def ASC');
        $this->pagedata['addressList'] = $addressList;
        
        $this->pagedata['return_type'] = $return_type;
        $this->pagedata['reason'] = app::get('ome')->model('refund_reason')->getList('reason',[]);

        $this->pagedata['choose_type_value'] = $choose_type_value;
        $this->pagedata['choose_type_flag']  = $choose_type_flag;
        $this->pagedata['return_id']         = $return_id;
        $this->pagedata['return_bn']         = $reship['return_bn'];
        $this->pagedata['finder_id']         = $_GET['finder_iid'];
        $this->pagedata['status']            = $status;
        $this->pagedata['is_edit']           = 'false';
        
        //平台退货地址ID
        if(!$reship['address_id']){
            $addressInfo = kernel::single("ome_return")->getReturnAddr($shop_id, $reship['belong_store_id'], $reship['shop_type']);
            $reship['address_id'] = $addressInfo['contact_id'];
        }
        $this->pagedata['select_address_id'] = $reship['address_id'];
        
        $this->getRefundinfo($reship['order_id']);
        
        //售后服务类型
        $server_type = array('1' => '退货单', '2' => '换货单');
        if ($this->pagedata['order']['pay_status'] == '5') {
            unset($server_type['2'], $server_type['3']); //全额退款的订单只允许申请退货单
        }
        $this->pagedata['server_type'] = $server_type;
        $this->display('admin/return_product/choose_type.html');
    }

    public function getRefundinfo($orderid)
    {
        //判断是否为失败订单
        $api_failObj = $this->app->model('api_fail');
        $api_fail    = $api_failObj->dump(array('order_id' => $orderid, 'type' => 'payment'));
        if ($api_fail) {
            $api_fail_flag = 'true';
        } else {
            $api_fail_flag = 'false';
        }
        $this->pagedata['api_fail_flag'] = $api_fail_flag;

        $this->pagedata['orderid'] = $orderid;
        $objOrder                  = $this->app->model('orders');
        $aORet                     = $objOrder->order_detail($orderid);

        $aORet['cur_name'] = 'CNY';
        $aORet['cur_sign'] = 'CNY';

        $oPayment       = $this->app->model('payments');
        $payment_cfgObj = app::get('ome')->model('payment_cfg');
        $oShop          = $this->app->model('shop');
        $c2c_shop       = ome_shop_type::shop_list();
        $shop_id        = $aORet['shop_id'];
        $shop_detail    = $oShop->dump($shop_id, 'node_type,node_id');
        if ($shop_id && !in_array($shop_detail['node_type'], $c2c_shop)) {
            $payment = kernel::single('ome_payment_type')->paymethod($shop_id);
        } else {
            $payment = $oPayment->getMethods();
        }

        $payment_cfg = $payment_cfgObj->dump(array('pay_bn' => $aORet['pay_bn']), 'id,pay_type');

        $this->pagedata['shop_id']    = $shop_id;
        $this->pagedata['node_id']    = $shop_detail['node_id'];
        $this->pagedata['payment']    = $payment;
        $this->pagedata['payment_id'] = $payment_cfg['id'];
        $this->pagedata['pay_type']   = $payment_cfg['pay_type'];
        if ($payment_cfg['id']) {
            $order_paymentcfg = kernel::single('ome_payment_type')->paymethod($shop_id, $payment_cfg['pay_type']);
        }
        $this->pagedata['order_paymentcfg'] = $order_paymentcfg;
        $this->pagedata['op_name']          = 'admin';
        $this->pagedata['typeList']         = ome_payment_type::pay_type();

        if ($aORet['member_id'] > 0) {
            $objMember                = $this->app->model('members');
            $aRet                     = $objMember->member_detail($aORet['member_id']);
            $this->pagedata['member'] = $aRet;
        } else {
            $this->pagedata['member'] = array();
        }
        $this->pagedata['order'] = $aORet;

        $aRet     = $oPayment->getAccount();
        $aAccount = array('--使用已存在帐户--');
        foreach ($aRet as $v) {
            $aAccount[$v['bank'] . "-" . $v['account']] = $v['bank'] . " - " . $v['account'];
        }
        $this->pagedata['pay_account'] = $aAccount;
        //剩余支付金额
        $pay_money                   = kernel::single('eccommon_math')->number_minus(array($aORet['total_amount'], $aORet['payed']));
        $this->pagedata['pay_money'] = $pay_money;
        $this->pagedata['aItems']    = $objOrder->getItemList($orderid);

    }

    //构造一个已发货订单列表供选择（新建售后申请和新建退换货，两处获取订单列表）
    public function getOrders()
    {
        $op_id       = kernel::single('desktop_user')->get_id();
        $this->title = '订单查看';
        $source      = trim($_GET['source']);
        if (in_array($source, array('archive'))) {
            //已发货、部分退货 并且 支付方式为已付款、部分退款、全额退款
            $base_filter = array('disabled' => 'false', 'is_fail' => 'false', 'ship_status' => array('1', '3'), 'pay_status' => array('1', '4', '5'));

            //check shop permission
            $organization_permissions = kernel::single('desktop_user')->get_organization_permission();
            if ($organization_permissions) {
                $base_filter['org_id'] = $organization_permissions;
            }

            $params = array(
                'title'                  => $this->title,
                'use_buildin_new_dialog' => false,
                'use_buildin_set_tag'    => false,
                'use_buildin_recycle'    => false,
                'use_buildin_export'     => false,
                'use_buildin_import'     => false,
                'use_buildin_filter'     => true,
                'use_view_tab'           => false,
                'finder_aliasname'       => 'order_view' . $op_id,
                'finder_cols'            => 'order_bn,shop_id,total_amount,process_status,is_cod,pay_status,ship_status,payment,shipping,logi_id,logi_no,createtime,paytime',
                'orderBy'                => 'order_id',
                'orderType'              => 'desc',
                'base_filter'            => $base_filter,
            );
            $this->finder('archive_mdl_orders', $params);
        } else {
            $base_filter = array('disabled'=>'false', 'is_fail'=>'false', 'ship_status'=>array('1', '3'));
            
            //支持货到付款--已发货的订单
            $base_filter['order_confirm_filter'] = "(pay_status IN('1', '4', '5') OR is_cod='true')";
            
            //check shop permission
            $organization_permissions = kernel::single('desktop_user')->get_organization_permission();
            if ($organization_permissions) {
                $base_filter['org_id'] = $organization_permissions;
            }

            $params = array(
                'title'                  => $this->title,
                'use_buildin_new_dialog' => false,
                'use_buildin_set_tag'    => false,
                'use_buildin_recycle'    => false,
                'use_buildin_export'     => false,
                'use_buildin_import'     => false,
                'use_buildin_filter'     => true,
                'use_view_tab'           => false,
                'finder_aliasname'       => 'order_view' . $op_id,
                'finder_cols'            => 'order_bn,shop_id,total_amount,column_print_status,process_status,is_cod,pay_status,ship_status,payment,shipping,logi_id,logi_no,createtime,paytime,mark_type',
                'orderBy'                => 'order_id',
                'orderType'              => 'desc',
                'base_filter'            => $base_filter,
            );
            $this->finder('ome_mdl_orders', $params);
        }
    }

    /**
     * 跳转至单据编辑页
     * @param INT $return_id 售后服务ID
     * @param String $type 单据类型(return:退货单，change:换货单)
     **/
    public function gotoreceipt($return_id, $type)
    {
        if (!in_array($type, array('return', 'change'))) {
            echo '单据类型错误!';exit;
        }
        $reship_id = $this->app->model('reship')->select()->columns('reship_id')->where('return_id=?', $return_id)->instance()->fetch_one();
        if (!$reship_id) {
            echo '单据不存在!';exit;
        }
        kernel::single('ome_ctl_admin_return_rchange')->edit($reship_id);
    }

    //售后入库单页面显示 （单据报表->仓库作业单据）
    public function return_io()
    {
        #增加单据导出权限
        $is_export        = kernel::single('desktop_user')->has_permission('bill_export');
        $this->workground = "invoice_center";

        $organization_permissions = kernel::single('desktop_user')->get_organization_permission();
        if ($organization_permissions) {
            $base_filter['org_id'] = $organization_permissions;
        }

        $params = array(
            'title'                  => '售后入库单',
            'use_buildin_new_dialog' => false,
            'use_buildin_set_tag'    => false,
            'use_buildin_recycle'    => false,
            'use_buildin_export'     => $is_export,
            'use_buildin_import'     => false,
            'use_buildin_filter'     => true,
            'base_filter'            => $base_filter,
        );
        $this->finder('ome_mdl_return_iostock', $params);
    }

    //新建售后页面弹窗（售后->售后  这里选择新建类型）
    public function create_return()
    {
        $this->page('admin/return_product/create_return.html');
    }

    //退款留言
    public function refund_message($apply_id, $type = 'return')
    {
        $this->pagedata['apply_id']  = $apply_id;
        $this->pagedata['type']      = $type;
        $this->pagedata['finder_id'] = $_GET['finder_id'];
        $this->pagedata['shop_type'] = $_GET['shop_type'];
        $this->display('admin/refund/plugin/refund_message.html');
    }

    //ajax下载信息
    public function download_message($apply_id, $type)
    {
        set_time_limit(0);
        $message_list = kernel::single('ome_service_aftersale')->refund_message($apply_id, $type);
        $shop_type    = $_POST['shop_type'];
        $online_memo  = array();
        $rsp          = array('rsp' => 'succ', 'msg' => '获取成功');
        if ($message_list) {
            $online_memo = serialize($message_list);
            $oRefund     = $this->app->model('refund_apply');
            $refund      = $oRefund->dump($apply_id, 'shop_id');
            $oShop       = $this->app->model('shop');
            $shop        = $oShop->dump(array('shop_id' => $refund['shop_id']));
            if ($type == 'refund') {
#退款
                if ($shop_type == 'tmall') {
                    $oRefund_apply_model = $this->app->model('refund_apply_tmall');
                } else {
                    $oRefund_apply_model = $this->app->model('refund_apply_taobao');
                }
                $result = $oRefund_apply_model->update(array('online_memo' => $online_memo), array('apply_id' => $apply_id));
            } else {
#退货
                if ($shop_type == 'tmall') {
                    $oRefund_apply_model = $this->app->model('return_product_tmall');
                } elseif ($shop_type == '360buy') {
                    $oRefund_apply_model = $this->app->model('return_product_360buy');
                } else {
                    $oRefund_apply_model = $this->app->model('return_product_taobao');
                }
                $result = $oRefund_apply_model->update(array('online_memo' => $online_memo), array('return_id' => $apply_id));
            }
            if (!$result) {
                $rsp = array('rsp' => 'fail', 'msg' => '获取失败,请稍后再试');
            }
        } else {
            $rsp = array('rsp' => 'fail', 'msg' => '暂无凭证');
        }
        echo json_encode($rsp);
    }

    //拒绝留言.
    public function refuse_message($return_id=null, $shop_type=null)
    {
        set_time_limit(0);
        if ($_POST) {
            $this->begin();
            $oProduct       = $this->app->model('return_product');
            $refuse_message = $_POST['refuse_message'];
            $return_id      = $_POST['return_id'];
            $shop_type      = $_POST['shop_type'];
            if (in_array($shop_type,array('taobao','tmall','luban'))) {
                if ($_FILES['refuse_proof']['size'] <= 0) {
                    $this->end(false,'请上传凭证图片!',null,array('status'=>'-1'));
                }
            }
            if ($shop_type == 'taobao') {
                $return_model = $this->app->model('return_product_taobao');
            } else if ($shop_type == 'tmall') {
                $return_model        = $this->app->model('return_product_tmall');
                $return_tmall        = $return_model->dump(array('return_id' => $return_id));
                $operation_contraint = $return_tmall['operation_contraint'];
                if ($operation_contraint) {
                    $operation_contraint = explode('|', $operation_contraint);
                    if (in_array('cannot_refuse', $operation_contraint)) {
                        $this->end(false,'此单据,不允许拒绝，必须同意',null,array('status'=>'-1'));
                    }
                    if (in_array('refund_onweb', $operation_contraint)) {
                        $this->end(false,'此单据,回到web页面上操作',null,array('status'=>'-1'));
                    }
                }
            } else if ($shop_type == 'yhd') {
                $return_model = $this->app->model('return_product_yihaodian');
            }else if($shop_type == 'luban'){
                $return_model = $this->app->model('return_product_luban');
            } else if ($shop_type == 'kaola') {
                $return_model = $this->app->model('return_product_kaola');
            }
            $refuse_memo['refuse_message'] = $refuse_message;
            if ($_FILES['refuse_proof']['size'] != 0) {
                if ($_FILES['refuse_proof']['size'] > 512000) {
                    $this->end(false,'上传文件不能超过500K',null,array('status'=>'-1'));
                }
                $type   = $type   = array('gif', 'jpg', 'png');
                $imgext = strtolower(substr(strrchr($_FILES['refuse_proof']['name'], '.'), 1));
                if ($_FILES['refuse_proof']['name']) {
                    if (!in_array($imgext, $type)) {
                        $text = implode(",", $type);
                        $this->end(false,"您只能上传以下类型文件{$text}!",null,array('status'=>'-1'));
                    }
                }

                $ss                   = kernel::single('base_storager');
                $id                   = $ss->save_upload($_FILES['refuse_proof'], "file", "", $msg); //返回file_id;
                $refuse_memo['image'] = $ss->getUrl($id, "file");
                if ($shop_type == 'tmall') {
                    $rh          = fopen($_FILES['refuse_proof']['tmp_name'], 'rb');
                    $imagebinary = fread($rh, filesize($_FILES['refuse_proof']['tmp_name']));
                    fclose($rh);
                    $imagebinary = base64_encode($imagebinary);

                } else {
                    $imagebinary = $refuse_memo['image'];
                }
            }
            $product = $oProduct->dump($return_id);
            if ($refuse_memo && $return_model) {
                $data = array(
                    'return_id'   => $return_id,
                    'shop_id'     => $product['shop_id'],
                    'refund_type'     => $product['return_type'],
                    'return_bn'   => $product['return_bn'],
                    'refuse_memo' => serialize($refuse_memo),
                    'imgext'      => $imgext,
                );

                $return_result = $return_model->save($data);

            }
            $aftersale_service = kernel::single('ome_service_aftersale');
            if (method_exists($aftersale_service, 'update_status')) {
                $memo['refuse_message'] = $refuse_message;
                $memo['refuse_proof']   = $imagebinary;
                if ($return_tmall['refund_type'] == 'change') {
                    $memo['seller_refuse_reason_id'] = $_POST['seller_refuse_reason_id'];
                    $memo['refund_type']             = 'kinds';
                }
                if(in_array($shop_type, array( 'luban','meituan4medicine'))){
                    $memo['reject_reason_code'] = $_POST['seller_refuse_reason_id'];
                    $memo['remark'] = $refuse_message;
                    unset($memo['refuse_message']);
                }


                //售后仅退款拒绝
                if ($product['return_type'] == 'refund') {
                    $orderMdl = app::get('ome')->model('orders');
                    $orderDetail = $orderMdl->getList('order_bn', array('order_id' => $product['order_id']), 0, 1);
                    $refundDendSdf = array(
                        'refund_refer'   => '1',
                        'refuse_proof'   => $imagebinary,
                        'oid'            => '',
                        'order_bn'       => $orderDetail[0]['order_bn'],
                        'refund_bn'      => $product['return_bn'],
                        'trigger_event'  => $memo['remark'],
                        'aftersale_refund_type' => 'aftersale_refund'
                    );
                    $rs = kernel::single('erpapi_router_request')->set('shop', $product['shop_id'])->finance_addRefund($refundDendSdf);
                } else {
                    $rs = $aftersale_service->update_status($return_id, '5', 'sync', $memo);
                }

                if (!$rs || $rs['rsp'] == 'fail') {
                    $this->app->model('operation_log')->write_log('return@ome', $return_id, '拒绝:' . $refuse_message);
                    $this->end(true,$rs['msg'] ?: $rs['error_msg'],null,array('status'=>'-2', 'autohide' => 5000));
                }
                $adata = array(
                    'return_id' => $return_id,
                    'shop_id'   => $product['shop_id'],
                    'status'    => '5',
                    'dend_reason' => $refuse_message
                );
                $oProduct->tosave($adata, true);
            }
            $this->end(true, '成功');
        }
        $this->pagedata['shop_type'] = $shop_type;
        if ($shop_type == 'tmall') {

            $aftersaleObj        = kernel::single('ome_service_aftersale');
            $return_tmall_detail = $aftersaleObj->get_return_type(array('return_id' => $return_id));
            if ($return_tmall_detail['refund_type'] == 'change') {
                $refuse_reason = $return_tmall_detail['refusereason'];
                if ($refuse_reason) {
                    $refuse_reason = json_decode($refuse_reason, true);
                } else {
                    $refuse_reason = $aftersaleObj->refuse_reason($return_id);
                }
                $this->pagedata['refuse_reason'] = $refuse_reason;
            }
        }elseif($shop_type == 'luban'){
            $returnModel = app::get('ome')->model('return_product');
            $returninfo = $returnModel->dump(array('return_id'=>$return_id,'source'=>'matrix'),'return_bn,return_id,shop_id');
            $refuse_reason = kernel::single('erpapi_router_request')->set('shop', $returninfo['shop_id'])->aftersale_getRefuseReason($returninfo);
            $this->pagedata['refuse_reason'] = $refuse_reason;
        }elseif(in_array($shop_type ,array( 'meituan4medicine'))){
            $refuse_reason = kernel::single('ome_aftersale_request_'.$shop_type)->getAftersaleReason('return_reship');
            $this->pagedata['refuse_reason'] = $refuse_reason;
        }
        $this->pagedata['return_id'] = $return_id;
        $this->pagedata['finder_id'] = $_GET['finder_id'];
        $this->display('admin/return_product/plugin/refuse_message.html');
    }

    //批量同步更新状态（“批量拒绝退货”已被注释）
    public function batch_syncUpdate()
    {
        $oReturn         = app::get('ome')->model('return_product');
        $oReturn_tmall   = app::get('ome')->model('return_product_tmall');
        $oReturn_address = app::get('ome')->model('return_address'); //cancel_def
        $oReturn_batch   = app::get('ome')->model('return_batch');
        $status_type     = $_GET['status_type'];
        if (!in_array($status_type, array('agree', 'refuse'))) {
            echo '暂不支持此状态变更';
            exit;
        }
        $shopArr = app::get('ome')->model('shop')->getList('shop_id', ['delivery_mode'=>'jingxiao']);
        $shopJXid = array_column($shopArr,'shop_id');
        $returnFilter = array('return_id' => $_POST['return_id']);
        if($shopJXid) {
            $returnFilter['shop_id|notin'] = $shopJXid;
        }
        $return_list = $oReturn->getlist('return_id,status,return_bn,source,shop_type,shop_id', $returnFilter);
        $chk_msg     = array();
        $error       = array();
        $addressList = array();
        
        //淘宝异步 天猫是即时的
        if ($status_type == 'agree') {
            //批量同意退货
            $shop_ids = array();
            foreach ($return_list as $return)
            {
                $shop_id = $return['shop_id'];
                $return_id = $return['return_id'];
                $status    = $return['status'];
                if (!in_array($status, array('1', '2'))) {
                    $error_msg = $return['return_bn'] . ':状态不可以批量接受退货申请';
                }
                
                //淘宝、天猫、抖音平台必须填地址等信息
                if (in_array($return['shop_type'], array('tmall', 'taobao', 'luban')) && $return['source'] == 'matrix') {
                    $return_address = $oReturn_address->dump(array('shop_id' => $return['shop_id'], 'cancel_def' => 'true'));
                    if (!$return_address) {
                        $chk_msg[] = '请为店铺设置默认退货地址,否则批量将无法操作';
                        break;
                    }
                }
                
                if ($error_msg) {
                    $error[] = array('error_msg' => $error_msg);
                }
                
                $shop_ids[$shop_id] = $shop_id;
            }
            
            //只支持同店铺批量同意操作
            if(count($shop_ids) > 1){
                die("不支持多个店铺同时操作批量同意。");
            }
            
            //获取退货地址列表
            $addressList = $oReturn_address->getList('*', array('shop_id'=>$shop_ids, 'reship_id'=>0), 0, 100, 'cancel_def ASC');
            
        } elseif ($status_type == 'refuse') {
            //批量拒绝退货
            foreach ($return_list as $return) {
                $return_id = $return['return_id'];
                $status    = $return['status'];
                if (!in_array($status, array('1', '2'))) {
                    $error_msg = $return['return_bn'] . ':状态不可以批量拒绝';
                }
                //淘宝天猫必须填地址等信息
                if (($return['shop_type'] == 'tmall' || $return['shop_type'] == 'taobao') && $return['source'] == 'matrix') {
                    $return_batch = $oReturn_batch->dump(array('shop_id' => $return['shop_id'], 'is_default' => 'true', 'batch_type' => 'refuse_return'));
                    if (!$return_batch) {
                        $chk_msg[] = '请为店铺设置默认拒绝凭证和留言!';
                        break;
                    }
                }
                if ($error_msg) {
                    $error[] = array('error_msg' => $error_msg);
                }
            }
        }

        $this->pagedata['error']   = $error;
        $this->pagedata['chk_msg'] = $chk_msg;
        
        //查询是否都是线上单据，是否淘宝和天猫
        //获取可操作数据
        $returnObj                                = kernel::single('ome_return_product');
        $need_return_list                         = $returnObj->return_list(array_column($return_list, 'return_id'));
        $this->pagedata['status_type']            = $status_type;
        $this->pagedata['need_return_list']       = json_encode($need_return_list);
        $this->pagedata['need_return_list_count'] = count($need_return_list);
        
        $this->pagedata['addressList'] = $addressList;
        $this->pagedata['finder_id'] = $_GET['finder_id'];
        $this->display('admin/return_product/plugin/batch_taobao.html');
    }

    //请求执行可操作
    public function ajax_batch()
    {
        set_time_limit(0);
        $returnObj  = kernel::single('ome_return_product');
        $data       = $_POST;
        $ajaxParams = trim($data['ajaxParams']);
        if (strpos($ajaxParams, ';')) {
            $params = explode(';', $ajaxParams);
        } else {
            $params = array($ajaxParams);
        }
        
        $status_type = $data['status_type'];
        $return_id = json_decode($data['return_id'], true);
        $address_id = intval($data['address_id']);
        
        //同意退货请求
        if ($status_type == 'agree') {
            //预先处理邮费承担方
            $post_data = array('post_fee_bear_role'=>$_POST['post_fee_bear_role'], 'address_id'=>$address_id);
            app::get('ome')->model('return_product')->batch_update_product_data($params, $post_data);
        }
        
        $rs = $returnObj->batch_update($status_type, $params);
        echo json_encode(array('total' => count($params), 'succ' => $rs['succ'], 'fail' => $rs['fail'], 'error_msg' => $rs['error_msg']));
    }

    //点击事件显示图片
    public function showImage($filepath)
    {
        echo "<img src='$filepath'>";
    }

    //更新退款单状态
    public function do_updateReturn($return_id, $status)
    {
        $oReturn = app::get('ome')->model('return_product');
        $return  = $oReturn->dump($return_id, 'return_id,shop_id');
        $adata   = array(
            'return_id' => $return_id,
            'shop_id'   => $return['shop_id'],
            'status'    => '5',
            'memo'      => '向线上请求拒绝失败,本地拒绝',
        );
        $oReturn->tosave($adata, true);
        $data = array('rsp' => 'succ');
        echo json_encode($data);
    }

    #设置运费承担方
    public function set_post_fee_bear_role($return_id)
    {
        $this->pagedata['finder_id'] = $_GET['finder_iid'];
        $this->pagedata['return_id'] = $return_id;

        $this->display('admin/return_product/post_fee_bear_role.html');
    }
    public function save_post_fee_bear_role()
    {
        $this->begin();
        $return_id          = $_POST['return_id'];
        $post_fee_bear_role = $_POST['post_fee_bear_role'];
        $params             = array('0||' . $return_id);
        app::get('ome')->model('return_product')->batch_update_product_data($params, array('post_fee_bear_role' => $post_fee_bear_role));
        $this->end(true, app::get('base')->_('操作成功'));
    }

    public function sync_aftersale()
    {

        $oShop     = $this->app->model('shop');
        $shop_list = $oShop->getlist('*', array('shop_type' => 'taobao', 'tbbusiness_type' => 'B'));
        if ($shop_list) {
            foreach ($shop_list as $k => $shop) {
                if ($shop['node_id'] == '') {
                    unset($shop_list[$k]);
                }
            }
        }

        $this->pagedata['shop']      = $shop_list;
        $this->pagedata['finder_id'] = $_GET['finder_id'];
        $this->display('admin/return_product/sync_aftersale.html');
    }

    public function do_sync_aftersale()
    {
        $shop_id      = $_POST['shop_id'];
        $aftersale_bn = trim($_POST['aftersale_bn']);
        $shopObj      = $this->app->model('shop');

        $shop_detail = $shopObj->dump(array('shop_id' => $shop_id), 'node_id,shop_id');

        $node_id     = $shop_detail['node_id'];
        if ($shop_detail) {
            $rsp_data = kernel::single('erpapi_router_request')->set('shop', $shop_detail['shop_id'])->aftersale_get_aftersale_detail(trim($aftersale_bn));

            if ($rsp_data['rsp'] == 'succ') {

                $sdf_exchange = $rsp_data['data']['exchange'];
                $rs           = kernel::single('erpapi_router_response')->set_node_id($node_id)->set_api_name('ome.exchange.add')->dispatch($sdf_exchange);
                echo json_encode(array('rsp' => 'succ', 'msg' => '单拉成功'));
                exit;

            } else {
                echo json_encode(array('rsp' => 'fail', 'msg' => $rsp_data['err_msg'] ? $rsp_data['err_msg'] : "同步售后单失败。"));
                exit;
            }

        }

    }

    public function hand_interception(){

        $oReturn         = app::get('ome')->model('return_product');
        $returnFilter = array('return_id' => $_POST['return_id']);
        $return_list = $oReturn->getlist('return_id,status,return_bn,source,shop_type,shop_id', $returnFilter);
        $interception_obj = app::get('ome')->model('interception');
        $intLib = kernel::single('ome_interception');
        $interception_list = [];
        foreach ($return_list as $return_info){
            $return_id = $return_info['return_id'];
            $interception_info = $interception_obj->dump(['return_id'=>$return_id]);
            if($interception_info){
                $err = '';
                $result =  $intLib->interception_express($return_id,$err);
                if($err!='' || $result!=true){
                    //失败
                    $interception_list[] = "申请单".$return_info['return_bn'].":".$err;
                }else{
                    $interception_list[] = "申请单".$return_info['return_bn'].": 拦截成功";
                }
            }else{
                $interception_list[] = "申请单".$return_info['return_bn'].": 不再拦截列表中";
            }
        }

        $this->pagedata['interception_list'] = $interception_list;
        $this->display('admin/return_product/hand_interception.html');
    }

    public function refund_agree(){
        $return_id = $_POST['return_id'];
        $returnMdl = app::get('ome')->model("return_product");
        $reshipMdl = app::get("ome")->model("reship");
        $returnInfo = $returnMdl->dump($return_id, "*");
        $rs = array(
            'rsp' => 'succ',
            'msg' => '操作成功',
        );
        if(!$returnInfo){
            $rs['rsp'] = 'fail';
            $rs['msg'] = '售后申请单不存在';
            echo json_encode($rs);exit;
        }
        if($returnInfo['platform_status'] == 'SUCCESS' || $returnInfo['platform_status'] == '12'){
            $rs['rsp'] = 'fail';
            $rs['msg'] = '平台已退款，无需同步';
            echo json_encode($rs);exit;
        }
        if($returnInfo['intercept_status'] != '3'){
            $rs['rsp'] = 'fail';
            $rs['msg'] = '非拦截成功状态，无需同步';
            echo json_encode($rs);exit;
        }
        $reshipInfo = $reshipMdl->dump(array("return_id" => $return_id), "*");
        if(!$reshipInfo){
            $rs['rsp'] = 'fail';
            $rs['msg'] = '无退货单，无需同步';
            echo json_encode($rs);exit;
        }
        if(!($reshipInfo['status'] == 'ready' && in_array($reshipInfo['is_check'], ['0','1']))){
            $rs['rsp'] = 'fail';
            $rs['msg'] = '退货单非待入库状态，无需同步';
            echo json_encode($rs);exit;
        }
        $status = '4';
        $rsp = kernel::single('ome_service_aftersale')->update_status($return_id, $status, 'sync');
        if ($rsp  && $rsp['rsp'] == 'fail')
        {
            $rs['rsp'] = 'fail';
            $rs['msg'] = $rsp['msg'];
        }
        $this->app->model('operation_log')->write_log('return@ome', $return_id, '操作退款同意:' . $rs['rsp'].$rs['msg']);
        echo json_encode($rs);exit;
    }

    /**
     * 需拦截列表 - 显示退款类型且待拦截状态的售后申请单
     */
    public function interception_list()
    {
        $base_filter = array(
            'return_type' => 'refund',        // 退款类型
            'intercept_status' => '1',        // 待拦截状态
            'is_fail' => 'false'              // 非失败单据
        );

        //check shop permission
        $organization_permissions = kernel::single('desktop_user')->get_organization_permission();
        if ($organization_permissions) {
            $base_filter['org_id'] = $organization_permissions;
        }

        // 获取云仓
        $store = app::get('o2o')->model('store')->getList('store_id', ['delivery_type'=>'branch']);
        $base_filter['belong_store_id'] = array_column($store, 'store_id');

        $action[] = array(
            'label'  => '手动拦截登记',
            'submit' => 'index.php?app=ome&ctl=admin_return&act=manual_intercept_register',
            'target' => "dialog::{width:600,height:400,title:'手动拦截登记'}",
        );

        $params = array(
            'title'                  => '需拦截列表',
            'use_buildin_new_dialog' => false,
            'use_buildin_set_tag'    => false,
            'use_buildin_recycle'    => false,
            'use_buildin_export'     => true,
            'use_buildin_import'     => false,
            'use_buildin_filter'     => true,
            'use_view_tab'           => false,
            'actions'                => $action,
            'base_filter'            => $base_filter,
            'orderBy'                => 'add_time DESC',
        );

        $this->finder('ome_mdl_return_product', $params);
    }

    /**
     * 手动拦截登记页面
     */
    public function manual_intercept_register()
    {
        //check
        if ($_POST['isSelectedAll'] == '_ALL_') {
            die('不能使用全选功能,每次最多选择1条!');
        }

        $ids = $_POST['return_id'];
        if (count($ids) > 1) {
            die('每次最多只能选择1条!');
        }

        $return_id = $ids[0];
        $return_product_obj = app::get('ome')->model('return_product');
        $return_info = $return_product_obj->dump(['return_id' => $return_id], 'return_bn,return_type,intercept_status,belong_store_id');

        // 验证是否为退款单且待拦截状态
        if ($return_info['return_type'] != 'refund') {
            die('仅退款单支持手动拦截登记!');
        }

        if ($return_info['intercept_status'] != '1') {
            die('仅待拦截状态的售后单支持手动拦截登记!');
        }

        // 验证仓库类型，必须是云仓
        $store = app::get('o2o')->model('store')->dump(['store_id' => $return_info['belong_store_id'],'delivery_type'=>'branch'], 'store_id');
        if (!$store) {
            die('仅云仓的售后申请单支持手动拦截登记!');
        }

        $this->pagedata['return_info'] = $return_info;
        $this->pagedata['return_id'] = $return_id;
        $this->display('admin/return/manual_intercept_register.html');
    }

    /**
     * 执行手动拦截登记
     */
    public function do_manual_intercept_register()
    {
        $this->begin('index.php?app=ome&ctl=admin_return&act=interception_list');

        $return_id = $_POST['return_id'];
        $logi_no = trim($_POST['logi_no']);

        if (!$return_id || !$logi_no) {
            $this->end(false, '售后单ID和快递单号必填');
        }

        // 验证售后单
        $return_product_obj = app::get('ome')->model('return_product');
        $return_info = $return_product_obj->dump(['return_id' => $return_id], 'return_type,intercept_status,belong_store_id');

        if ($return_info['return_type'] != 'refund') {
            $this->end(false, '仅退款单支持手动拦截登记');
        }

        if ($return_info['intercept_status'] != '1') {
            $this->end(false, '仅待拦截状态的售后单支持手动拦截登记');
        }

        // 验证仓库类型，必须是云仓
        $store = app::get('o2o')->model('store')->dump(['store_id' => $return_info['belong_store_id'],'delivery_type'=>'branch'], 'store_id');
        if (!$store) {
            $this->end(false, '仅云仓的售后申请单支持手动拦截登记');
        }

        // 验证快递单号:最少8位,只允许字母和数字
        if (!preg_match('/^[a-zA-Z0-9]{8,}$/', $logi_no)) {
            $this->end(false, '快递单号必须至少8位且只能包含字母和数字');
        }

        // 1. 更新售后申请单的拦截状态为拦截成功(3)
        $return_update_data = array(
            'intercept_status' => '3',  // 拦截成功
            'intercept_type' => 'hand', // 手动拦截
            'intercept_logi_no' => $logi_no,
            'last_modified' => time()
        );
        $return_update_result = $return_product_obj->update($return_update_data, array('return_id' => $return_id));

        if (!$return_update_result) {
            $this->end(false, '更新售后申请单拦截状态失败');
        }

        // 2. 查找关联的未审核退换货单（一个售后申请单只会关联一个is_check=0的退换货单）
        $reship_obj = app::get('ome')->model('reship');
        $reship_info = $reship_obj->dump(['return_id' => $return_id, 'is_check' => '0'], 'reship_id,reship_bn');

        $reship_msg = '';
        if ($reship_info) {
            // 3. 更新退换货单的return_logi_no字段
            $reship_update_data = array('return_logi_no' => $logi_no);
            $reship_update_result = $reship_obj->update($reship_update_data, array('reship_id' => $reship_info['reship_id']));

            if (!$reship_update_result) {
                $this->end(false, '更新退换货单物流单号失败');
            }

            // 4. 审核退换货单
            $reship_lib = kernel::single('ome_reship');
            $confirm_params = array(
                'reship_id' => $reship_info['reship_id'],
                'status' => '1',  // 审核通过
                'exec_type' => 1  // 系统自动审核
            );

            $confirm_error_msg = '';
            $confirm_result = $reship_lib->confirm_reship($confirm_params, $confirm_error_msg);

            if (!$confirm_result) {
                $this->end(false, '审核退换货单失败：' . $confirm_error_msg);
            }

            $reship_msg = '，关联退换货单：' . $reship_info['reship_bn'] . '(已更新物流单号并审核)';

            // 记录操作日志
            app::get('ome')->model('operation_log')->write_log('reship@ome', $reship_info['reship_id'], '拦截登记成功，更新物流单号并自动审核');
        }

        // 记录售后申请单操作日志
        app::get('ome')->model('operation_log')->write_log('return@ome', $return_id, '手动拦截登记成功，物流单号：' . $logi_no);

        $final_msg = '拦截登记成功' . $reship_msg;

        $this->end(true, $final_msg);
    }
}
