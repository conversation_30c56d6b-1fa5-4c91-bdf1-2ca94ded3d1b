<?php

$db['order_coupon_wxshipin'] = array(
    'columns' => array(
        'id' => array(
            'type' => 'int unsigned',
            'required' => true,
            'pkey' => true,
            'extra' => 'auto_increment',
            'label' => 'ID',
            'width' => 110,
            'hidden' => true,
            'editable' => false,
        ),
        'order_id' => array(
            'type' => 'table:orders@ome',
            'required' => true,
            'editable' => false,
        ),
        'oid' => array(
            'type' => 'varchar(50)',
            'default' => '',
            'editable' => false,
            'label' => '子订单号',
        ),
        'platform_type' => array(
            'type' => 'varchar(50)',
            'label' => '平台优惠类型',
            'editable' => false,
            'filtertype' => 'yes',
            'filterdefault' => true,
        ),
        'type' => array(
            'type' => 'varchar(50)',
            'label' => '活动类型',
            'editable' => false,
            'filtertype' => 'yes',
            'filterdefault' => true,
        ),
        'coupon_type' => array(
            'type' => 'int',
            'default' => '0',
            'label' => '优惠类型',
            'comment' => '优惠类型：0-商家，1-平台，2-达人'
        ),
        'sub_type' => array(
            'type' => 'varchar(50)',
            'label' => '活动子类型',
            'editable' => false,
            'comment' => '活动子类型：sku-sku改价，order-订单改价'
        ),
        'coupon_id' => array(
            'type' => 'varchar(50)',
            'default' => '',
            'editable' => false,
            'label' => '优惠ID',
        ),
        'coupon_meta_id' => array(
            'type' => 'varchar(50)',
            'label' => '券批次ID',
        ),
        'coupon_name' => array(
            'type' => 'varchar(100)',
            'label' => '优惠名称',
            'default' => '',
            'editable' => false,
        ),
        'num' => array(
            'type' => 'number',
            'editable' => false,
            'label' => 'sku数量',
            'comment' => 'sku数量',
        ),
        'material_bn' => array(
            'type' => 'varchar(200)',
            'label' => '物料编码',
            'width' => 120,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
            'searchtype' => 'nequal',
            'filtertype' => 'normal',
            'filterdefault' => true,
        ),
        'coupon_amount' => array(
            'type' => 'money',
            'default' => '0.000',
            'label' => '优惠金额',
            'width' => 75,
        ),
        'create_time' => array(
            'type' => 'time',
            'label' => '创建时间',
            'width' => 130,
            'editable' => false,
            'filtertype' => 'time',
            'filterdefault' => true,
            'in_list' => true,
        ),
        'source' => array(
            'type' =>
                array(
                    'local' => '本地创建',
                    'rpc' => 'api请求',
                    'push' => '矩阵推送',
                ),
            'default' => 'local',
            'label' => '来源',
        ),
    ),
    'index' => array(
        'idx_order_id' => array('columns' => array('order_id')),
        'idx_oid' => array('columns' => array('oid')),
        'idx_platform_type' => array('columns' => array('platform_type')),
        'idx_type' => array('columns' => array('type')),
        'idx_create_time' => array('columns' => array('create_time')),
    ),
    'comment' => '小红书平台优惠明细表',
    'engine' => 'innodb',
    'version' => '$Rev:  $',
);