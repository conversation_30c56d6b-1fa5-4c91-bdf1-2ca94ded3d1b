<?php
/**
 * 修复发货单明细表的推送ESB金额
 */

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$db = kernel::database();
$sql = "select wi.item_id,o.order_bn,wi.bn,obj.oid,wd.delivery_bn,o.order_id,o.shop_type from sdb_wap_delivery_items as wi 
        left join sdb_wap_delivery as wd on wi.delivery_id=wd.delivery_id
        left join sdb_ome_orders as o on wd.order_bn=o.order_bn
        left join sdb_ome_order_objects as obj on o.order_id=obj.order_id and wi.bn=obj.bn
        where wi.esb_amount<=0";
$wiList = $db->select($sql);

$wdiMdl = app::get("wap")->model("delivery_items");
$odiMdl = app::get("ome")->model("delivery_items");
foreach($wiList as $wik => $wiv){
    $oid = $wiv['oid'];
    if($wiv['shop_type'] == 'wxshipin'){
        $oid = $wiv['order_id'].$wiv['oid'];
    }
    $s_sql = "select si.orderSn,si.eshopOrderSn,si.orderPrice,si.discAmount from sdb_sales_sap_order_items as si 
            left join sdb_sales_sap_orders as so on si.sap_id=so.sap_id
            where si.orderSn='".$wiv['order_bn']."' and eshopOrderSn='".$oid."' 
            and so.bill_type='payed' and so.sync_status='succ'";
    $siInfo = $db->selectrow($s_sql);
    if(!empty($siInfo)){
        $uData = array(
            'esb_amount' => $siInfo['orderPrice'],
            'esb_pmt_amount' => $siInfo['discAmount'],
        );
        $uFilter = array(
            'item_id' => $wiv['item_id']
        );
        #$res = $wdiMdl->update($uData, $uFilter);
        $msg = "wap发货单号：".$wiv['delivery_bn']." 订单号：".$siInfo['orderSn']." 货号：".$wiv['bn']." 子订单号：".$siInfo['eshopOrderSn'].
            " 更新esb金额：".$siInfo['orderPrice']." 更新esb优惠金额：".$siInfo['discAmount']." 结果：".$res;
        echo $msg."\n";
        error_log(__LINE__.__FUNCTION__.'=>>时间：'.date('Y-m-d H:i:s').var_export($msg, 1)."\n", 3, DATA_DIR.'/checkesb.log');
    }
}

$o_sql = "select di.item_id,di.bn,o.order_bn,obj.oid,d.delivery_bn,o.order_id,o.shop_type from sdb_ome_delivery_items as di 
          left join sdb_ome_delivery as d on d.delivery_id=di.delivery_id
          left join sdb_ome_delivery_order as do on di.delivery_id=do.delivery_id
          left join sdb_ome_orders as o on do.order_id=o.order_id
          left join sdb_ome_order_objects as obj on obj.order_id=o.order_id and obj.bn=di.bn
          where di.esb_amount<=0";
$odList = $db->select($o_sql);
foreach($odList as $odk => $odv){
    $oid = $odv['oid'];
    if($odv['shop_type'] == 'wxshipin'){
        $oid = $odv['order_id'].$odv['oid'];
    }
    $s_sql = "select si.orderSn,si.eshopOrderSn,si.orderPrice,si.discAmount from sdb_sales_sap_order_items as si 
            left join sdb_sales_sap_orders as so on si.sap_id=so.sap_id
            where si.orderSn='".$odv['order_bn']."' and eshopOrderSn='".$oid."' 
            and so.bill_type='payed' and so.sync_status='succ'";
    $siInfo = $db->selectrow($s_sql);
    if(!empty($siInfo)){
        $uData = array(
            'esb_amount' => $siInfo['orderPrice'],
            'esb_pmt_amount' => $siInfo['discAmount'],
        );
        $uFilter = array(
            'item_id' => $odv['item_id']
        );
        #$res = $odiMdl->update($uData, $uFilter);
        $msg = "ome发货单：".$odv['delivery_bn']." 订单号：".$siInfo['orderSn']." 货号：".$wiv['bn']." 子订单号：".$siInfo['eshopOrderSn'].
            " 更新esb金额：".$siInfo['orderPrice']." 更新esb优惠金额：".$siInfo['discAmount']." 结果：".$res;
        echo $msg."\n";
        error_log(__LINE__.__FUNCTION__.'=>>时间：'.date('Y-m-d H:i:s').var_export($msg, 1)."\n", 3, DATA_DIR.'/checkesb.log');
    }
}