<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bns = [
    '6940774631864145798',
    '6940778980341520262',
    '6940774620309165958'
];

$orderMdl = app::get('ome')->model('orders');
$orderList = $orderMdl->getList('order_id,order_bn,shop_type', array('order_bn' => $order_bns));
foreach ($orderList as $order) {
    $sdf = kernel::single('ome_sap_data_platform_' . $order['shop_type'])->get_order_sdf($order['order_id']);
    $result = kernel::single('erpapi_router_request')->set('sap', true)->order_push($sdf);
    if ($result['rsp'] == 'fail') {
        echo '订单号：' . $order['order_bn'] . ',推送sap失败：' . $result['msg'] . PHP_EOL;
        continue;
    }

    echo '订单号：' . $order['order_bn'] . ',推送成功' . PHP_EOL;
}

echo 'end...' . PHP_EOL;
