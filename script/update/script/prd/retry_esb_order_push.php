<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);


$sap_ids = [
    16164,
];

$sapOrderMdl = app::get('sales')->model('sap_orders');
$sapOrderItemsMdl = app::get('sales')->model('sap_order_items');
$sapGoodsItemsMdl = app::get('sales')->model('sap_good_items');
$sapPaymentsMdl = app::get('sales')->model('sap_payments');
$sapCouponsMdl = app::get('sales')->model('sap_coupons');
$rsaObj = kernel::single('erpapi_sap_rsa');

$sapOrderList = $sapOrderMdl->getList('*', array('sap_id' => $sap_ids));
if (empty($sapOrderList)) {
    exit('esb订单数据不存在' . PHP_EOL);
}

foreach ($sapOrderList as $sapOrderInfo){
    $filter = [
        'sap_id' => $sapOrderInfo['sap_id']
    ];
    $sap_order_items = $sapOrderItemsMdl->getList('*', $filter);
    if (empty($sap_order_items)) {
        exit('esb订单明细数据不存在' . PHP_EOL);
    }


    $data = [];
    foreach ($sap_order_items as $item) {
        $orderHead = [
            'orderSource' => $item['orderSource'],
            'orderType' => $item['orderType'], // 固定值
            'orderSn' => $item['orderSn'],  // 订单编号
            'mallId' => $item['mallId'], // 门店Id
            'shopId' => $item['shopId'], // 商户id
            'spuId' => $item['spuId'], // 商品大码
            'vipId' => $item['vipId'], // 会员卡号
            'orderTime' => $item['orderTime'], // 订单下单/退单时间 长度：13位
            'orderPrice' => floatval($item['orderPrice']), // 订单总金额
            'discAmount' => floatval($item['discAmount']), // 订单优惠金额
            'transAmount' => floatval($item['transAmount']), // 运费
            'bankAmount' => floatval($item['bankAmount']), // 银行手续费
            'oriFlowNo' => $item['oriFlowNo'], // 流水号，如果是退款单，则传订单号
            'eshopOrderSn' => $item['eshopOrderSn'],
            'payAmount' => floatval($item['payAmount']),
        ];
        $data['orderItems'][] = $orderHead;
    }

    # sku明细
    $sap_goods_items = $sapGoodsItemsMdl->getList('*', $filter);
    if (!empty($sap_goods_items)) {
        foreach ($sap_goods_items as $item) {
            # 订单明细
            $orderItem = [
                'eshopOrderSn' => $item['eshopOrderSn'], // 在线商城业务订单编号
                'spuId' => $item['spuId'], // 商品大码
                'sku' => $item['sku'], // 商品SKU信息
                'count' => intval($item['count']), // 订单购买商品数量
                'unitPrice' => floatval($item['unitPrice']), // 商品单价
                'mallId' => $item['mallId'], // 门店Id
                'totalPrice' => floatval($item['totalPrice']),
            ];
            $data['goodItems'][] = $orderItem;
        }
    }

    # 优惠列表
    $sap_payments = $sapPaymentsMdl->getList('*', $filter);
    if (!empty($sap_payments)) {
        foreach ($sap_payments as $item) {
            $payment = [
                'eshopOrderSn' => $item['eshopOrderSn'], // 业务订单编号
                'paymentType' => $item['paymentType'], // 金蝶支付方式名称(DP支付名称)
                'paymentCode' => $item['paymentCode'], // 金蝶支付方式编码，包含平台补贴(金蝶支付编码)
                'originPaymentType' => $item['originPaymentType'], // 原始支付方式名称
                'originPaymentCode' => $item['originPaymentCode'], // 原始支付方式编码
                'amount' => floatval($item['amount']), // 支付金额
                'subsidyAmount' => floatval($item['subsidyAmount']), // 平台补贴金额
            ];
            # 券号
            if (!empty($item['couponCode'])) {
                $payment['couponCode'] = $item['couponCode'];
            }
            $data['paymentItems'][] = $payment;
        }
    }

    # 优惠券列表
    $sap_coupons = $sapCouponsMdl->getList('*', $filter);
    if (!empty($sap_coupons)) {
        foreach ($sap_coupons as $item) {
            $coupon = [
                'eshopOrderSn' => $item['eshopOrderSn'],
                'couponCode' => $item['couponCode'],  // 券ID，非用户优惠券ID
                'couponName' => $item['couponName'],
                'couponAmount' => $item['couponAmount'],
                'couponType' => $item['couponType']
            ];
            $data['couponItems'][] = $coupon;
        }
    }

    $params = [
        'appId' => 'DY',
        'timestamp' => erpapi_sap_func::mixtimestamp(true),
        'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
    ];

    # data加密
    $params['data'] = $rsaObj->rsa_encode($sapOrderInfo['shop_type'], $params['data']);
    # 签名
    $params['sign'] = $rsaObj->gen_sign($sapOrderInfo['shop_type'], $params);

    $result = kernel::single('erpapi_router_request')->set('sap', true)->order_onlyPush($sapOrderInfo['sap_id'], $params);
    print_r($result);
}




