<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$channel_id = 7;
$params = [
    'delivery_id' => 'SF',
    'acct_id' => '6635764288',
];
$channelData = [
    'shop_id' => 'f532837b4c25ffb52eda6c67d1e3cce4',
    'company_code' => $params['delivery_id'],
];
$sdf = kernel::single('erpapi_router_request')->set('waplogistics', $channelData)->electron_getWaybillISearch($params);
print_r($sdf);