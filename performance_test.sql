-- 性能测试：整型字段使用字符串查询的影响

-- 1. 创建测试表
CREATE TABLE IF NOT EXISTS performance_test (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);

-- 2. 插入测试数据
INSERT INTO performance_test (user_id, name) VALUES 
(1, 'User 1'),
(2, 'User 2'),
(3, 'User 3'),
(100, 'User 100'),
(1000, 'User 1000'),
(10000, 'User 10000');

-- 3. 测试查询性能
-- 整型查询
EXPLAIN SELECT * FROM performance_test WHERE user_id = 100;

-- 字符串查询
EXPLAIN SELECT * FROM performance_test WHERE user_id = '100';

-- 4. 性能对比查询
-- 使用 BENCHMARK 函数测试（MySQL）
SELECT BENCHMARK(1000000, (SELECT COUNT(*) FROM performance_test WHERE user_id = 100)) AS int_query_time;
SELECT BENCHMARK(1000000, (SELECT COUNT(*) FROM performance_test WHERE user_id = '100')) AS string_query_time;

-- 5. 检查索引使用情况
SHOW INDEX FROM performance_test;

-- 6. 查看查询统计信息
SHOW STATUS LIKE 'Slow_queries';
SHOW STATUS LIKE 'Select_scan'; 